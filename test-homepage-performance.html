<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页性能测试 - 广州迅屿科技</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        /* 测试页面专用样式 */
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .test-section {
            margin-bottom: 3rem;
            padding: 2rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            background: white;
        }
        
        .test-title {
            color: #1e293b;
            margin-bottom: 1rem;
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .performance-stats {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
        }
        
        .test-instructions {
            background: #f1f5f9;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 2rem;
        }
        
        .mouse-tracker {
            position: fixed;
            top: 50%;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="performance-stats" id="performanceStats">
        <div><strong>首页性能测试</strong></div>
        <div>鼠标延迟: <span id="mouseLatency">--</span>ms</div>
        <div>FPS: <span id="currentFPS">--</span></div>
        <div>事件计数: <span id="eventCount">0</span></div>
        <div>优化状态: <span id="optimizationStatus">--</span></div>
    </div>

    <div class="mouse-tracker" id="mouseTracker">
        <div>鼠标位置: <span id="mousePos">--</span></div>
        <div>最后延迟: <span id="lastLatency">--</span>ms</div>
        <div>平均延迟: <span id="avgLatency">--</span>ms</div>
    </div>

    <div class="test-container">
        <div class="test-instructions">
            <h2>首页鼠标响应性能测试</h2>
            <p><strong>测试目标：</strong>验证首页鼠标响应延迟优化效果，目标是将延迟降低到100ms以下。</p>
            <p><strong>测试方法：</strong></p>
            <ol>
                <li>快速移动鼠标悬停在下方的测试元素上</li>
                <li>观察左上角的性能统计数据</li>
                <li>重点关注"鼠标延迟"指标</li>
                <li>测试不同类型的交互元素</li>
            </ol>
        </div>

        <!-- 解决方案卡片测试 -->
        <div class="test-section">
            <div class="test-title">解决方案卡片测试</div>
            <div class="test-grid">
                <div class="solution-card">
                    <h3>智能制造解决方案</h3>
                    <p>为制造业提供智能化升级服务</p>
                </div>
                <div class="solution-card">
                    <h3>数字化转型</h3>
                    <p>助力企业实现数字化转型</p>
                </div>
                <div class="solution-card">
                    <h3>云计算服务</h3>
                    <p>提供稳定可靠的云计算平台</p>
                </div>
            </div>
        </div>

        <!-- 优势展示测试 -->
        <div class="test-section">
            <div class="test-title">优势展示测试</div>
            <div class="test-grid">
                <div class="advantage-item">
                    <h4>技术领先</h4>
                    <p>拥有行业领先的技术实力</p>
                </div>
                <div class="advantage-item">
                    <h4>服务专业</h4>
                    <p>提供专业的技术服务支持</p>
                </div>
                <div class="advantage-item">
                    <h4>经验丰富</h4>
                    <p>多年行业经验积累</p>
                </div>
            </div>
        </div>

        <!-- 合作品牌测试 -->
        <div class="test-section">
            <div class="test-title">合作品牌测试</div>
            <div class="test-grid">
                <div class="partner-item">
                    <img src="https://via.placeholder.com/120x60/2196F3/white?text=Partner+1" alt="合作伙伴1">
                </div>
                <div class="partner-item">
                    <img src="https://via.placeholder.com/120x60/4CAF50/white?text=Partner+2" alt="合作伙伴2">
                </div>
                <div class="partner-item">
                    <img src="https://via.placeholder.com/120x60/FF9800/white?text=Partner+3" alt="合作伙伴3">
                </div>
                <div class="partner-item">
                    <img src="https://via.placeholder.com/120x60/9C27B0/white?text=Partner+4" alt="合作伙伴4">
                </div>
            </div>
        </div>

        <!-- 按钮测试 -->
        <div class="test-section">
            <div class="test-title">按钮交互测试</div>
            <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                <button class="btn btn-primary">主要按钮</button>
                <button class="btn btn-outline">次要按钮</button>
                <button class="btn btn-more">了解更多</button>
            </div>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script>
        // 专门的性能测试脚本
        class MouseLatencyTester {
            constructor() {
                this.latencies = [];
                this.eventCount = 0;
                this.lastMouseDown = 0;
                this.init();
            }

            init() {
                // 监听鼠标事件
                document.addEventListener('mousedown', (e) => {
                    this.lastMouseDown = performance.now();
                    this.eventCount++;
                });

                document.addEventListener('mouseup', (e) => {
                    if (this.lastMouseDown > 0) {
                        const latency = performance.now() - this.lastMouseDown;
                        this.latencies.push(latency);
                        this.updateDisplay();
                        
                        // 保持最近50个测量值
                        if (this.latencies.length > 50) {
                            this.latencies.shift();
                        }
                    }
                });

                // 监听鼠标位置
                document.addEventListener('mousemove', (e) => {
                    document.getElementById('mousePos').textContent = `${e.clientX}, ${e.clientY}`;
                });

                // FPS监控
                this.monitorFPS();
            }

            updateDisplay() {
                const latest = this.latencies[this.latencies.length - 1];
                const average = this.latencies.reduce((sum, val) => sum + val, 0) / this.latencies.length;

                document.getElementById('mouseLatency').textContent = average.toFixed(1);
                document.getElementById('lastLatency').textContent = latest.toFixed(1);
                document.getElementById('avgLatency').textContent = average.toFixed(1);
                document.getElementById('eventCount').textContent = this.eventCount;

                // 更新优化状态
                const status = average < 100 ? '优秀' : average < 150 ? '良好' : '需要优化';
                document.getElementById('optimizationStatus').textContent = status;
            }

            monitorFPS() {
                let lastTime = performance.now();
                let frameCount = 0;

                const measureFPS = () => {
                    frameCount++;
                    const currentTime = performance.now();
                    
                    if (currentTime - lastTime >= 1000) {
                        const fps = frameCount;
                        document.getElementById('currentFPS').textContent = fps;
                        frameCount = 0;
                        lastTime = currentTime;
                    }
                    
                    requestAnimationFrame(measureFPS);
                };

                measureFPS();
            }
        }

        // 启动测试
        document.addEventListener('DOMContentLoaded', () => {
            new MouseLatencyTester();
        });
    </script>
</body>
</html>
