// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 显示页面加载动画
    showPageLoadingAnimation();

    // 初始化所有功能
    initScrollProgress();
    initBackToTop();
    initSearchBox();
    initSmoothScroll();
    initAnimationOnScroll();
    initLoadingAnimations();
    initParticleBackground();
    initMouseFollower();
    initEnhancedProductCards();
    initParallaxEffects();

    initMagneticButtons();
    initAdvancedScrollAnimations();
    initFeaturedProductsInteraction();
    initBrandCards3D();
    initMobileOptimizations();
});

// 移动端优化
function initMobileOptimizations() {
    // 检测移动设备和特殊浏览器
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth <= 768;
    const isXiaomiBrowser = /MiuiBrowser|XiaoMi|Mi\s/i.test(navigator.userAgent) ||
                           navigator.userAgent.includes('MIUI') ||
                           navigator.userAgent.includes('Mi Browser');
    const isDesktopMode = window.innerWidth > 768 && (isMobile || isXiaomiBrowser);

    // 额外的小米浏览器检测
    const isLikelyXiaomi = window.navigator.userAgent.includes('Android') &&
                          (window.navigator.userAgent.includes('Mi ') ||
                           window.navigator.userAgent.includes('Redmi') ||
                           window.navigator.userAgent.includes('MIUI'));

    if (isMobile || isDesktopMode || isLikelyXiaomi) {
        // 添加移动端标识类
        document.body.classList.add('mobile-device');

        if (isXiaomiBrowser || isDesktopMode || isLikelyXiaomi) {
            document.body.classList.add('xiaomi-browser');
            console.log('小米浏览器检测：启用特殊优化模式');
        }

        // 禁用所有可能导致性能问题的功能
        disablePerformanceHeavyFeatures();

        // 优化触摸事件
        optimizeTouchEvents();

        // 防止缩放循环（特别针对小米浏览器）
        preventScalingLoop();

        // 优化滚动性能
        optimizeScrollPerformance();

        // 简化DOM操作
        simplifyDOMOperations();
    }
}

// 禁用性能密集型功能
function disablePerformanceHeavyFeatures() {
    const style = document.createElement('style');
    style.id = 'mobile-performance-optimizations';
    style.textContent = `
        .mobile-device * {
            animation: none !important;
            transition: none !important;
            transform: none !important;
            filter: none !important;
            backdrop-filter: none !important;
            -webkit-backdrop-filter: none !important;
            will-change: auto !important;
            perspective: none !important;
            transform-style: flat !important;
        }

        .mobile-device .floating-shape,
        .mobile-device .light-beam,
        .mobile-device .particle-canvas,
        .mobile-device .custom-cursor,
        .mobile-device .scroll-indicator {
            display: none !important;
        }

        .mobile-device .dynamic-background::before,
        .mobile-device .hero::after,
        .mobile-device .featured-products::after {
            display: none !important;
        }

        /* 小米浏览器特殊处理 */
        .xiaomi-browser {
            overflow-x: hidden !important;
        }

        .xiaomi-browser * {
            max-width: 100vw !important;
            box-sizing: border-box !important;
        }
    `;
    document.head.appendChild(style);
}

// 优化触摸事件
function optimizeTouchEvents() {
    // 使用被动监听器提升性能
    const passiveEvents = ['touchstart', 'touchmove', 'touchend', 'scroll', 'wheel'];
    passiveEvents.forEach(event => {
        document.addEventListener(event, function() {}, {passive: true});
    });

    // 禁用不必要的触摸事件
    document.addEventListener('touchstart', function(e) {
        // 防止多点触控导致的问题
        if (e.touches.length > 1) {
            e.preventDefault();
        }
    }, {passive: false});
}

// 防止缩放循环
function preventScalingLoop() {
    // 固定viewport设置
    let viewportMeta = document.querySelector('meta[name="viewport"]');
    if (viewportMeta) {
        viewportMeta.setAttribute('content',
            'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover'
        );
    }

    // 小米浏览器特殊处理
    if (document.body.classList.contains('xiaomi-browser')) {
        handleXiaomiBrowserIssues();
    }

    // 防止resize事件导致的循环
    let resizeTimer;
    let lastWidth = window.innerWidth;
    let resizeCount = 0;

    window.addEventListener('resize', function() {
        clearTimeout(resizeTimer);
        resizeCount++;

        // 如果resize事件过于频繁，强制停止处理
        if (resizeCount > 10) {
            console.log('检测到异常resize事件，停止处理以防止循环');
            return;
        }

        resizeTimer = setTimeout(function() {
            resizeCount = 0;

            // 只有在宽度真正改变时才处理
            if (Math.abs(window.innerWidth - lastWidth) > 10) {
                lastWidth = window.innerWidth;

                // 重置可能导致循环的样式
                document.querySelectorAll('.dynamic-background, .hero, .featured-products').forEach(el => {
                    el.style.transform = 'none';
                    el.style.animation = 'none';
                    el.style.willChange = 'auto';
                });
            }
        }, 200);
    }, {passive: true});
}

// 小米浏览器特殊问题处理
function handleXiaomiBrowserIssues() {
    // 强制禁用所有可能导致问题的CSS属性
    const forceDisableStyle = document.createElement('style');
    forceDisableStyle.id = 'xiaomi-browser-fix';
    forceDisableStyle.textContent = `
        * {
            transform: none !important;
            animation: none !important;
            transition: none !important;
            will-change: auto !important;
            perspective: none !important;
            transform-style: flat !important;
            backface-visibility: visible !important;
        }

        html, body {
            overflow-x: hidden !important;
            position: relative !important;
            width: 100% !important;
            max-width: 100vw !important;
        }

        .dynamic-background::before,
        .dynamic-background::after,
        .hero::before,
        .hero::after,
        .featured-products::before,
        .featured-products::after {
            display: none !important;
        }
    `;
    document.head.appendChild(forceDisableStyle);

    // 监听orientationchange事件
    window.addEventListener('orientationchange', function() {
        setTimeout(function() {
            // 强制重新计算布局
            document.body.style.display = 'none';
            document.body.offsetHeight; // 触发重排
            document.body.style.display = '';
        }, 100);
    });

    // 禁用双击缩放
    let lastTouchEnd = 0;
    document.addEventListener('touchend', function(event) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
            event.preventDefault();
        }
        lastTouchEnd = now;
    }, false);
}

// 优化滚动性能
function optimizeScrollPerformance() {
    // 使用requestAnimationFrame优化滚动处理
    let ticking = false;

    function updateScrollElements() {
        // 最小化滚动时的DOM操作
        ticking = false;
    }

    window.addEventListener('scroll', function() {
        if (!ticking) {
            requestAnimationFrame(updateScrollElements);
            ticking = true;
        }
    }, {passive: true});
}

// 简化DOM操作
function simplifyDOMOperations() {
    // 禁用复杂的初始化函数
    const functionsToDisable = [
        'initParticleBackground',
        'initAdvancedScrollAnimations',
        'initFeaturedProductsInteraction'
    ];

    functionsToDisable.forEach(funcName => {
        if (window[funcName]) {
            window[funcName] = function() {
                // 空函数，不执行任何操作
                return;
            };
        }
    });
}

// 热门产品区域交互效果
function initFeaturedProductsInteraction() {
    const featuredSection = document.querySelector('.featured-products');
    if (!featuredSection) return;

    const floatingShapes = featuredSection.querySelectorAll('.floating-shape');

    featuredSection.addEventListener('mousemove', (e) => {
        const rect = featuredSection.getBoundingClientRect();
        const x = (e.clientX - rect.left) / rect.width;
        const y = (e.clientY - rect.top) / rect.height;

        floatingShapes.forEach((shape, index) => {
            const intensity = 20 + index * 10;
            const moveX = (x - 0.5) * intensity;
            const moveY = (y - 0.5) * intensity;

            shape.style.transform = `translate(${moveX}px, ${moveY}px) rotate(${moveX * 0.5}deg)`;
        });
    });

    featuredSection.addEventListener('mouseleave', () => {
        floatingShapes.forEach(shape => {
            shape.style.transform = '';
        });
    });
}



// 页面滚动进度条
function initScrollProgress() {
    // 创建进度条元素
    const progressBar = document.createElement('div');
    progressBar.className = 'scroll-progress';
    document.body.appendChild(progressBar);

    // 监听滚动事件
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
        const progress = (scrollTop / scrollHeight) * 100;

        progressBar.style.width = Math.min(progress, 100) + '%';
    });
}

// 回到顶部按钮
function initBackToTop() {
    // 创建回到顶部按钮
    const backToTopBtn = document.createElement('button');
    backToTopBtn.className = 'back-to-top';
    backToTopBtn.innerHTML = '<i class="fas fa-chevron-up"></i>';
    backToTopBtn.setAttribute('aria-label', '回到顶部');
    document.body.appendChild(backToTopBtn);

    // 监听滚动事件，控制按钮显示
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopBtn.classList.add('visible');
        } else {
            backToTopBtn.classList.remove('visible');
        }
    });

    // 点击回到顶部
    backToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// 平滑滚动到锚点
function initSmoothScroll() {
    // 为所有内部链接添加平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                const headerHeight = document.querySelector('header').offsetHeight;
                const targetPosition = target.offsetTop - headerHeight - 20;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// 滚动触发动画
function initAnimationOnScroll() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // 观察需要动画的元素
    document.querySelectorAll('.product-card, .solution-card, .advantage-item, .partner-item').forEach(el => {
        observer.observe(el);
    });
}

// 加载动画
function initLoadingAnimations() {
    // 为图片添加加载效果
    document.querySelectorAll('img').forEach(img => {
        if (!img.complete) {
            img.classList.add('loading-shimmer');
            img.addEventListener('load', function() {
                this.classList.remove('loading-shimmer');
                this.classList.add('animate-fade-in');
            });
        }
    });
}

// 初始化搜索框功能（增强版）
function initSearchBox() {
    const searchInput = document.querySelector('.search-box input');
    const searchButton = document.querySelector('.search-box button');

    if (!searchInput || !searchButton) return;

    // 搜索功能
    function performSearch() {
        const query = searchInput.value.trim();
        if (query) {
            // 添加搜索动画
            searchButton.style.transform = 'scale(0.95)';
            setTimeout(() => {
                searchButton.style.transform = '';
                window.location.href = `search.html?q=${encodeURIComponent(query)}`;
            }, 150);
        }
    }

    // 点击搜索按钮
    searchButton.addEventListener('click', performSearch);

    // 按回车键搜索
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });

    // 搜索建议功能（可选）
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            // 这里可以添加搜索建议逻辑
            console.log('搜索建议:', this.value);
        }, 300);
    });
}

// 工具函数：节流
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// 工具函数：防抖
function debounce(func, wait, immediate) {
    let timeout;
    return function() {
        const context = this, args = arguments;
        const later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}

// 页面加载动画
function showPageLoadingAnimation() {
    // 检查是否是首次访问
    const hasSeenLoader = sessionStorage.getItem('hasSeenLoader');

    if (hasSeenLoader) {
        // 如果已经看过加载动画，直接显示页面内容
        document.body.classList.add('loaded');
        return;
    }

    // 标记用户已经看过加载动画
    sessionStorage.setItem('hasSeenLoader', 'true');

    // 创建加载动画容器
    const loader = document.createElement('div');
    loader.className = 'page-loader';
    loader.innerHTML = `
        <div class="loader-content">
            <div class="loader-logo">
                <img src="LOGO_全.png" alt="广州迅屿科技">
            </div>
            <div class="loader-progress">
                <div class="progress-bar"></div>
            </div>
        </div>
    `;
    document.body.appendChild(loader);

    // 模拟加载进度
    const progressBar = loader.querySelector('.progress-bar');
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress >= 100) {
            progress = 100;
            clearInterval(interval);
            setTimeout(() => {
                loader.classList.add('fade-out');
                setTimeout(() => {
                    document.body.removeChild(loader);
                    document.body.classList.add('loaded');
                }, 500);
            }, 300);
        }
        progressBar.style.width = progress + '%';
    }, 100);
}

// 粒子背景效果
function initParticleBackground() {
    const hero = document.querySelector('.hero');
    if (!hero) return;

    const canvas = document.createElement('canvas');
    canvas.className = 'particle-canvas';
    hero.appendChild(canvas);

    const ctx = canvas.getContext('2d');
    let particles = [];
    let mouse = { x: 0, y: 0 };

    // 设置画布大小
    function resizeCanvas() {
        canvas.width = hero.offsetWidth;
        canvas.height = hero.offsetHeight;
    }
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // 粒子类
    class Particle {
        constructor() {
            this.x = Math.random() * canvas.width;
            this.y = Math.random() * canvas.height;
            this.vx = (Math.random() - 0.5) * 0.5;
            this.vy = (Math.random() - 0.5) * 0.5;
            this.size = Math.random() * 2 + 1;
            this.opacity = Math.random() * 0.5 + 0.2;
        }

        update() {
            this.x += this.vx;
            this.y += this.vy;

            // 边界检测
            if (this.x < 0 || this.x > canvas.width) this.vx *= -1;
            if (this.y < 0 || this.y > canvas.height) this.vy *= -1;

            // 鼠标交互
            const dx = mouse.x - this.x;
            const dy = mouse.y - this.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            if (distance < 100) {
                this.x -= dx * 0.01;
                this.y -= dy * 0.01;
            }
        }

        draw() {
            ctx.beginPath();
            ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
            ctx.fillStyle = `rgba(255, 255, 255, ${this.opacity})`;
            ctx.fill();
        }
    }

    // 创建粒子
    for (let i = 0; i < 50; i++) {
        particles.push(new Particle());
    }

    // 鼠标移动事件
    hero.addEventListener('mousemove', (e) => {
        const rect = hero.getBoundingClientRect();
        mouse.x = e.clientX - rect.left;
        mouse.y = e.clientY - rect.top;
    });

    // 动画循环
    function animate() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        particles.forEach(particle => {
            particle.update();
            particle.draw();
        });

        // 连接近距离的粒子
        for (let i = 0; i < particles.length; i++) {
            for (let j = i + 1; j < particles.length; j++) {
                const dx = particles[i].x - particles[j].x;
                const dy = particles[i].y - particles[j].y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < 100) {
                    ctx.beginPath();
                    ctx.moveTo(particles[i].x, particles[i].y);
                    ctx.lineTo(particles[j].x, particles[j].y);
                    ctx.strokeStyle = `rgba(255, 255, 255, ${0.1 * (1 - distance / 100)})`;
                    ctx.stroke();
                }
            }
        }

        requestAnimationFrame(animate);
    }
    animate();
}

// 鼠标跟随效果
function initMouseFollower() {
    const cursor = document.createElement('div');
    cursor.className = 'custom-cursor';
    document.body.appendChild(cursor);

    let mouseX = 0, mouseY = 0;
    let cursorX = 0, cursorY = 0;

    document.addEventListener('mousemove', (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;
    });

    function animateCursor() {
        cursorX += (mouseX - cursorX) * 0.1;
        cursorY += (mouseY - cursorY) * 0.1;
        cursor.style.left = cursorX + 'px';
        cursor.style.top = cursorY + 'px';
        requestAnimationFrame(animateCursor);
    }
    animateCursor();

    // 悬停效果
    document.querySelectorAll('a, button, .btn').forEach(el => {
        el.addEventListener('mouseenter', () => {
            cursor.classList.add('cursor-hover');
        });
        el.addEventListener('mouseleave', () => {
            cursor.classList.remove('cursor-hover');
        });
    });
}

// 增强产品卡片交互
function initEnhancedProductCards() {
    const cards = document.querySelectorAll('.product-card, .pro-card');

    cards.forEach(card => {
        card.addEventListener('mouseenter', function(e) {
            this.style.transform = 'translateY(-15px) rotateX(5deg) rotateY(5deg)';
            this.style.boxShadow = '0 25px 50px rgba(0,0,0,0.2)';

            // 添加光影效果
            const light = document.createElement('div');
            light.className = 'card-light';
            this.appendChild(light);
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';

            const light = this.querySelector('.card-light');
            if (light) light.remove();
        });

        card.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const centerX = rect.width / 2;
            const centerY = rect.height / 2;

            const rotateX = (y - centerY) / 10;
            const rotateY = (centerX - x) / 10;

            this.style.transform = `translateY(-15px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
        });
    });
}

// 视差滚动效果（简化版，避免视觉断层）
function initParallaxEffects() {
    // 暂时禁用视差效果以避免空白间隙问题
    // 保留函数结构以便将来重新启用
    return;
}



// 磁性按钮效果
function initMagneticButtons() {
    const buttons = document.querySelectorAll('.btn, button');

    buttons.forEach(button => {
        button.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left - rect.width / 2;
            const y = e.clientY - rect.top - rect.height / 2;

            this.style.transform = `translate(${x * 0.1}px, ${y * 0.1}px)`;
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
    });
}

// 高级滚动动画
function initAdvancedScrollAnimations() {
    // 文字打字机效果
    const typewriterElements = document.querySelectorAll('.hero-content h1');

    typewriterElements.forEach(element => {
        const text = element.textContent;
        element.textContent = '';
        element.style.borderRight = '2px solid white';

        let i = 0;
        const typeInterval = setInterval(() => {
            element.textContent += text.charAt(i);
            i++;
            if (i >= text.length) {
                clearInterval(typeInterval);
                setTimeout(() => {
                    element.style.borderRight = 'none';
                }, 500);
            }
        }, 100);
    });

    // 滚动触发的元素动画
    const animatedElements = document.querySelectorAll('.solution-card, .advantage-item, .partner-item');

    const scrollObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.classList.add('animate-slide-up');
                }, index * 100);
                scrollObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1 });

    animatedElements.forEach(el => {
        scrollObserver.observe(el);
    });

    // 滚动进度指示器
    const sections = document.querySelectorAll('section');
    const progressIndicator = document.createElement('div');
    progressIndicator.className = 'scroll-indicator';
    progressIndicator.innerHTML = sections.length > 0 ?
        Array.from(sections).map((_, i) => `<div class="indicator-dot" data-section="${i}"></div>`).join('') : '';
    document.body.appendChild(progressIndicator);

    window.addEventListener('scroll', throttle(() => {
        const scrollTop = window.pageYOffset;
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = scrollTop / docHeight;

        // 更新当前活动的section
        sections.forEach((section, index) => {
            const rect = section.getBoundingClientRect();
            const dot = progressIndicator.querySelector(`[data-section="${index}"]`);
            if (dot) {
                if (rect.top <= window.innerHeight / 2 && rect.bottom >= window.innerHeight / 2) {
                    dot.classList.add('active');
                } else {
                    dot.classList.remove('active');
                }
            }
        });
    }, 100));
}

// 增强轮播图效果
function enhanceHeroSwiper() {
    // 如果Swiper已经初始化，增强其效果
    const swiperContainer = document.querySelector('.hero-swiper');
    if (swiperContainer && window.Swiper) {
        // 添加自定义过渡效果
        swiperContainer.addEventListener('slideChange', function() {
            const activeSlide = this.querySelector('.swiper-slide-active');
            if (activeSlide) {
                activeSlide.classList.add('slide-animate');
                setTimeout(() => {
                    activeSlide.classList.remove('slide-animate');
                }, 1000);
            }
        });
    }
}

// 页面可见性API - 当页面重新获得焦点时播放动画
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        // 重新触发一些动画
        document.querySelectorAll('.animate-fade-in').forEach(el => {
            el.style.animation = 'none';
            setTimeout(() => {
                el.style.animation = '';
            }, 10);
        });
    }
});

// 性能优化：使用requestIdleCallback进行非关键任务
if (window.requestIdleCallback) {
    requestIdleCallback(() => {
        // 预加载图片
        const images = document.querySelectorAll('img[data-src]');
        images.forEach(img => {
            const src = img.getAttribute('data-src');
            if (src) {
                const newImg = new Image();
                newImg.onload = () => {
                    img.src = src;
                    img.classList.add('loaded');
                };
                newImg.src = src;
            }
        });
    });
}

// 品牌卡片3D交互效果
function initBrandCards3D() {
    const brandCards = document.querySelectorAll('.partner-item');

    brandCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transformStyle = 'preserve-3d';
        });

        card.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const centerX = rect.width / 2;
            const centerY = rect.height / 2;

            const rotateX = (y - centerY) / 8;
            const rotateY = (centerX - x) / 8;

            this.style.transform = `translateY(-8px) scale(1.05) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
    });
}