// 性能监控和验证脚本
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            fps: [],
            scrollLatency: [],
            mouseLatency: [],
            memoryUsage: [],
            animationFrames: 0,
            lastFrameTime: performance.now()
        };
        
        this.isMonitoring = false;
        this.startTime = 0;
        this.init();
    }

    init() {
        // 检测设备类型
        this.deviceType = this.detectDeviceType();
        
        // 创建性能监控面板
        this.createMonitorPanel();
        
        // 开始监控
        this.startMonitoring();
        
        // 监听用户交互
        this.setupEventListeners();
    }

    detectDeviceType() {
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth <= 768;
        const isTablet = window.innerWidth > 768 && window.innerWidth <= 1024;
        const isDesktop = window.innerWidth > 1024;
        
        return {
            isMobile,
            isTablet,
            isDesktop,
            screenWidth: window.innerWidth,
            screenHeight: window.innerHeight,
            devicePixelRatio: window.devicePixelRatio || 1
        };
    }

    createMonitorPanel() {
        // 只在开发环境或URL包含debug参数时显示
        if (!window.location.search.includes('debug=true')) return;
        
        const panel = document.createElement('div');
        panel.id = 'performance-monitor';
        panel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            min-width: 200px;
            max-height: 300px;
            overflow-y: auto;
        `;
        
        panel.innerHTML = `
            <div><strong>性能监控</strong></div>
            <div>设备: ${this.deviceType.isMobile ? '移动端' : this.deviceType.isTablet ? '平板' : '桌面端'}</div>
            <div>分辨率: ${this.deviceType.screenWidth}x${this.deviceType.screenHeight}</div>
            <div>像素比: ${this.deviceType.devicePixelRatio}</div>
            <div id="fps-display">FPS: --</div>
            <div id="memory-display">内存: --</div>
            <div id="scroll-display">滚动延迟: --</div>
            <div id="mouse-display">鼠标延迟: --</div>
            <div id="animation-display">动画帧: --</div>
            <button onclick="performanceMonitor.exportReport()" style="margin-top: 5px; padding: 2px 5px;">导出报告</button>
        `;
        
        document.body.appendChild(panel);
    }

    startMonitoring() {
        this.isMonitoring = true;
        this.startTime = performance.now();
        this.monitorFPS();
        this.monitorMemory();
    }

    monitorFPS() {
        const measureFPS = () => {
            if (!this.isMonitoring) return;
            
            const currentTime = performance.now();
            const deltaTime = currentTime - this.lastFrameTime;
            const fps = 1000 / deltaTime;
            
            this.metrics.fps.push(fps);
            this.metrics.animationFrames++;
            this.lastFrameTime = currentTime;
            
            // 保持最近100帧的数据
            if (this.metrics.fps.length > 100) {
                this.metrics.fps.shift();
            }
            
            this.updateDisplay();
            requestAnimationFrame(measureFPS);
        };
        
        requestAnimationFrame(measureFPS);
    }

    monitorMemory() {
        if (!performance.memory) return;
        
        setInterval(() => {
            if (!this.isMonitoring) return;
            
            const memory = {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit
            };
            
            this.metrics.memoryUsage.push(memory);
            
            // 保持最近50个内存快照
            if (this.metrics.memoryUsage.length > 50) {
                this.metrics.memoryUsage.shift();
            }
        }, 1000);
    }

    setupEventListeners() {
        // 监控滚动性能
        let scrollStartTime = 0;
        window.addEventListener('wheel', () => {
            scrollStartTime = performance.now();
        }, { passive: true });
        
        window.addEventListener('scroll', () => {
            if (scrollStartTime > 0) {
                const latency = performance.now() - scrollStartTime;
                this.metrics.scrollLatency.push(latency);
                scrollStartTime = 0;
                
                // 保持最近50个滚动延迟数据
                if (this.metrics.scrollLatency.length > 50) {
                    this.metrics.scrollLatency.shift();
                }
            }
        }, { passive: true });
        
        // 监控鼠标响应性能
        let mouseStartTime = 0;
        document.addEventListener('mousedown', () => {
            mouseStartTime = performance.now();
        });
        
        document.addEventListener('mouseup', () => {
            if (mouseStartTime > 0) {
                const latency = performance.now() - mouseStartTime;
                this.metrics.mouseLatency.push(latency);
                mouseStartTime = 0;
                
                // 保持最近50个鼠标延迟数据
                if (this.metrics.mouseLatency.length > 50) {
                    this.metrics.mouseLatency.shift();
                }
            }
        });
    }

    updateDisplay() {
        const panel = document.getElementById('performance-monitor');
        if (!panel) return;
        
        const avgFPS = this.getAverage(this.metrics.fps);
        const avgScrollLatency = this.getAverage(this.metrics.scrollLatency);
        const avgMouseLatency = this.getAverage(this.metrics.mouseLatency);
        const currentMemory = this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1];
        
        document.getElementById('fps-display').textContent = `FPS: ${avgFPS.toFixed(1)}`;
        document.getElementById('scroll-display').textContent = `滚动延迟: ${avgScrollLatency.toFixed(1)}ms`;
        document.getElementById('mouse-display').textContent = `鼠标延迟: ${avgMouseLatency.toFixed(1)}ms`;
        document.getElementById('animation-display').textContent = `动画帧: ${this.metrics.animationFrames}`;
        
        if (currentMemory) {
            const memoryMB = (currentMemory.used / 1024 / 1024).toFixed(1);
            document.getElementById('memory-display').textContent = `内存: ${memoryMB}MB`;
        }
    }

    getAverage(array) {
        if (array.length === 0) return 0;
        return array.reduce((sum, value) => sum + value, 0) / array.length;
    }

    exportReport() {
        const report = {
            timestamp: new Date().toISOString(),
            deviceInfo: this.deviceType,
            testDuration: performance.now() - this.startTime,
            metrics: {
                averageFPS: this.getAverage(this.metrics.fps),
                minFPS: Math.min(...this.metrics.fps),
                maxFPS: Math.max(...this.metrics.fps),
                averageScrollLatency: this.getAverage(this.metrics.scrollLatency),
                averageMouseLatency: this.getAverage(this.metrics.mouseLatency),
                totalAnimationFrames: this.metrics.animationFrames,
                memoryUsage: this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1]
            },
            performance: this.evaluatePerformance()
        };
        
        console.log('性能报告:', report);
        
        // 下载报告
        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `performance-report-${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    evaluatePerformance() {
        const avgFPS = this.getAverage(this.metrics.fps);
        const avgScrollLatency = this.getAverage(this.metrics.scrollLatency);
        const avgMouseLatency = this.getAverage(this.metrics.mouseLatency);
        
        const evaluation = {
            overall: 'good',
            issues: []
        };
        
        if (avgFPS < 30) {
            evaluation.overall = 'poor';
            evaluation.issues.push('FPS过低，可能存在性能问题');
        } else if (avgFPS < 50) {
            evaluation.overall = 'fair';
            evaluation.issues.push('FPS偏低，建议优化动画');
        }
        
        if (avgScrollLatency > 16) {
            evaluation.overall = 'poor';
            evaluation.issues.push('滚动延迟过高');
        }
        
        if (avgMouseLatency > 100) {
            evaluation.overall = 'poor';
            evaluation.issues.push('鼠标响应延迟过高');
        }
        
        return evaluation;
    }

    stop() {
        this.isMonitoring = false;
    }
}

// 自动初始化性能监控
let performanceMonitor;
document.addEventListener('DOMContentLoaded', () => {
    performanceMonitor = new PerformanceMonitor();
});
