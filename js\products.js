// 产品中心页面脚本
document.addEventListener('DOMContentLoaded', function() {
    // 初始化现代化交互效果
    initModernInteractions();

    // 初始化产品分类导航
    initProductCategories();

    // 初始化品牌筛选功能
    initBrandFilter();

    // 处理URL哈希值，自动跳转到对应产品区域
    handleUrlHash();

    // 默认显示所有产品（如果没有URL哈希值）
    if (!window.location.hash) {
        showAllProducts();
    }
});

// 初始化现代化交互效果
function initModernInteractions() {
    // 为所有产品项添加增强的CSS类
    const productItems = document.querySelectorAll('.product-item');
    productItems.forEach(item => {
        item.classList.add('enhanced-hover', 'light-trail', 'gpu-accelerated');
    });

    // 为所有图片添加懒加载
    const images = document.querySelectorAll('.product-item img');
    images.forEach(img => {
        if (!img.hasAttribute('loading')) {
            img.setAttribute('loading', 'lazy');
        }
    });

    // 为所有按钮添加磁性效果
    const buttons = document.querySelectorAll('.btn, .btn-more, .btn-primary, .btn-outline');
    buttons.forEach(button => {
        button.classList.add('magnetic-button');
    });

    // 初始化产品卡片的3D悬停效果
    initEnhancedProductCards();

    // 初始化鼠标跟随效果
    initMouseFollower();

    // 初始化磁性按钮效果
    initMagneticButtons();

    // 初始化滚动动画
    initScrollAnimations();
}

// 初始化品牌筛选功能
function initBrandFilter() {
    const brandCheckboxes = document.querySelectorAll('.brand-checkbox');
    const allBrandsCheckbox = document.getElementById('brand-all');
    const brandFilterTitle = document.querySelector('.sidebar-title:first-child');
    
    // 为品牌筛选标题添加点击事件，实现折叠功能
    if (brandFilterTitle) {
        brandFilterTitle.addEventListener('click', function() {
            this.classList.toggle('collapsed');
        });
    }
    
    // 为所有品牌选择框添加事件监听
    brandCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            // 如果点击的是"全部品牌"
            if (this.id === 'brand-all') {
                // 如果选中"全部品牌"，取消选中其他品牌
                if (this.checked) {
                    brandCheckboxes.forEach(cb => {
                        if (cb.id !== 'brand-all') {
                            cb.checked = false;
                        }
                    });
                } else {
                    // 如果取消选中"全部品牌"，不允许，重新选中
                    this.checked = true;
                }
            } else {
                // 如果点击的是具体品牌
                // 如果选中了任何具体品牌，取消选中"全部品牌"
                if (this.checked) {
                    allBrandsCheckbox.checked = false;
                } else {
                    // 检查是否所有具体品牌都未选中，如果是，则选中"全部品牌"
                    const anyBrandSelected = Array.from(brandCheckboxes).some(cb => 
                        cb.id !== 'brand-all' && cb.checked
                    );
                    
                    if (!anyBrandSelected) {
                        allBrandsCheckbox.checked = true;
                    }
                }
            }
            
            // 应用筛选
            applyBrandFilter();
        });
    });
    
    // 默认选中"全部品牌"
    allBrandsCheckbox.checked = true;
}

// 应用品牌筛选 - 增强版本
function applyBrandFilter() {
    const allBrandsCheckbox = document.getElementById('brand-all');
    const brandCheckboxes = document.querySelectorAll('.brand-checkbox:not(#brand-all)');

    // 获取选中的品牌
    const selectedBrands = [];
    if (allBrandsCheckbox.checked) {
        // 如果选中了"全部品牌"，显示所有产品
        showAllProducts();
        return;
    } else {
        // 获取选中的具体品牌
        brandCheckboxes.forEach(cb => {
            if (cb.checked) {
                selectedBrands.push(cb.id.replace('brand-', ''));
            }
        });
    }

    // 如果有选中的品牌，显示该品牌的所有产品
    if (selectedBrands.length > 0) {
        showBrandProducts(selectedBrands);
    } else {
        // 如果没有选中任何品牌，显示所有产品
        showAllProducts();
    }
}

// 显示指定品牌的所有产品
function showBrandProducts(selectedBrands) {
    // 隐藏欢迎信息和统一产品容器
    const welcomeSection = document.getElementById('product-welcome');
    const unifiedContainer = document.getElementById('unified-products-container');

    if (welcomeSection) {
        welcomeSection.style.display = 'none';
    }
    if (unifiedContainer) {
        unifiedContainer.style.display = 'none';
    }

    // 获取所有产品区域
    const allProductSections = document.querySelectorAll('.product-section');
    let hasVisibleProducts = false;

    // 遍历所有产品区域
    allProductSections.forEach(section => {
        const productItems = section.querySelectorAll('.product-item');
        let sectionHasVisibleProducts = false;

        // 检查该区域是否有匹配品牌的产品
        productItems.forEach(item => {
            const brand = item.getAttribute('data-brand');
            if (brand && selectedBrands.includes(brand)) {
                item.classList.remove('filtered');
                sectionHasVisibleProducts = true;
                hasVisibleProducts = true;
            } else {
                item.classList.add('filtered');
            }
        });

        // 如果该区域有可见产品，显示该区域
        if (sectionHasVisibleProducts) {
            section.style.display = 'block';
        } else {
            section.style.display = 'none';
        }
    });

    // 如果没有找到任何匹配的产品，显示提示信息
    if (!hasVisibleProducts) {
        showNoProductsMessage(selectedBrands);
    } else {
        hideNoProductsMessage();
    }

    // 清除菜单项的激活状态
    document.querySelectorAll('.category-submenu a').forEach(item => {
        item.classList.remove('active');
    });
}

// 显示默认视图
function showDefaultView() {
    // 显示欢迎信息
    const welcomeSection = document.getElementById('product-welcome');
    const unifiedContainer = document.getElementById('unified-products-container');

    if (welcomeSection) {
        welcomeSection.style.display = 'block';
    }

    // 隐藏统一产品容器
    if (unifiedContainer) {
        unifiedContainer.style.display = 'none';
    }

    // 隐藏所有产品区域
    const allProductSections = document.querySelectorAll('.product-section');
    allProductSections.forEach(section => {
        section.style.display = 'none';
    });

    // 移除所有产品的筛选状态
    const productItems = document.querySelectorAll('.product-item');
    productItems.forEach(item => {
        item.classList.remove('filtered');
    });

    // 清除菜单项的激活状态
    document.querySelectorAll('.category-submenu a').forEach(item => {
        item.classList.remove('active');
    });

    // 隐藏无产品提示
    hideNoProductsMessage();
}

// 显示所有产品（新增功能）
function showAllProducts() {
    // 隐藏欢迎信息
    const welcomeSection = document.getElementById('product-welcome');
    if (welcomeSection) {
        welcomeSection.style.display = 'none';
    }

    // 隐藏所有分类区域
    const allProductSections = document.querySelectorAll('.product-section');
    allProductSections.forEach(section => {
        section.style.display = 'none';
    });

    // 显示统一产品容器
    const unifiedContainer = document.getElementById('unified-products-container');
    const unifiedGrid = document.getElementById('unified-products-grid');

    if (unifiedContainer && unifiedGrid) {
        // 清空统一网格容器
        unifiedGrid.innerHTML = '';

        // 收集所有有实际产品的产品项
        const allRealProducts = [];
        allProductSections.forEach(section => {
            const productItems = section.querySelectorAll('.product-item:not(.empty-category)');
            productItems.forEach(item => {
                // 克隆产品项以避免从原位置移除
                const clonedItem = item.cloneNode(true);
                clonedItem.classList.remove('filtered');
                allRealProducts.push(clonedItem);
            });
        });

        // 将所有产品添加到统一网格中
        allRealProducts.forEach(product => {
            unifiedGrid.appendChild(product);
        });

        // 显示统一产品容器
        unifiedContainer.style.display = 'block';
    }

    // 清除菜单项的激活状态
    document.querySelectorAll('.category-submenu a').forEach(item => {
        item.classList.remove('active');
    });

    // 隐藏无产品提示
    hideNoProductsMessage();
}

// 显示无产品提示信息
function showNoProductsMessage(selectedBrands) {
    hideNoProductsMessage(); // 先清除现有的提示

    const brandNames = selectedBrands.map(brand => {
        const brandMap = {
            'uni-t': '优利德',
            'tektronix': '泰克',
            'maiknow': '脉知技术',
            'hioki': '日置',
            'fluke': '福禄克',
            'cybertek': '知用电子',
            'zhide': '致德测控',
            'zhixin': '致新精密'
        };
        return brandMap[brand] || brand;
    }).join('、');

    const message = document.createElement('div');
    message.className = 'no-products-message';
    message.innerHTML = `
        <div class="no-products-content">
            <i class="fas fa-search"></i>
            <h3>暂无 ${brandNames} 品牌产品</h3>
            <p>请尝试选择其他品牌或联系我们了解更多产品信息</p>
            <button class="btn btn-primary" onclick="document.getElementById('brand-all').click()">查看所有产品</button>
        </div>
    `;

    const mainContent = document.querySelector('.products-main-content');
    if (mainContent) {
        mainContent.appendChild(message);
    }
}

// 隐藏无产品提示信息
function hideNoProductsMessage() {
    const existingMessage = document.querySelector('.no-products-message');
    if (existingMessage) {
        existingMessage.remove();
    }
}

// 初始化产品分类导航
function initProductCategories() {
    const categoryHeaders = document.querySelectorAll('.category-header');

    // 设置所有分类默认为折叠状态
    document.querySelectorAll('.sidebar-category').forEach(category => {
        category.classList.add('collapsed');
    });
    
    categoryHeaders.forEach(header => {
        // 点击分类标题切换折叠/展开状态
        header.addEventListener('click', function(e) {
            // 阻止事件冒泡，避免点击箭头时触发链接跳转
            e.preventDefault();
            
            const category = this.parentElement;
            
            // 切换折叠类
            category.classList.toggle('collapsed');
        });
    });
    
    // 子菜单项点击事件 - 增强版本
    const submenuLinks = document.querySelectorAll('.category-submenu a');
    submenuLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // 获取目标产品区域ID
            const targetId = this.getAttribute('href');

            // 如果是页内链接，滚动到对应位置并显示对应产品
            if (targetId && targetId.startsWith('#')) {
                e.preventDefault();

                // 检查是否有选中的品牌
                const selectedBrands = getSelectedBrands();

                if (selectedBrands.length > 0) {
                    // 如果有选中的品牌，显示该品牌在该分类下的产品
                    showBrandCategoryProducts(selectedBrands, targetId);
                } else {
                    // 如果没有选中品牌，显示该分类的所有产品
                    showCategoryProducts(targetId);
                }

                // 高亮显示当前选中的菜单项
                submenuLinks.forEach(item => {
                    item.classList.remove('active');
                });
                this.classList.add('active');
            }
        });
    });
}

// 获取当前选中的品牌
function getSelectedBrands() {
    const allBrandsCheckbox = document.getElementById('brand-all');
    const brandCheckboxes = document.querySelectorAll('.brand-checkbox:not(#brand-all)');
    const selectedBrands = [];

    if (!allBrandsCheckbox.checked) {
        brandCheckboxes.forEach(cb => {
            if (cb.checked) {
                selectedBrands.push(cb.id.replace('brand-', ''));
            }
        });
    }

    return selectedBrands;
}

// 显示指定品牌在指定分类下的产品
function showBrandCategoryProducts(selectedBrands, targetId) {
    // 隐藏欢迎信息和统一产品容器
    const welcomeSection = document.getElementById('product-welcome');
    const unifiedContainer = document.getElementById('unified-products-container');

    if (welcomeSection) {
        welcomeSection.style.display = 'none';
    }
    if (unifiedContainer) {
        unifiedContainer.style.display = 'none';
    }

    // 隐藏所有产品区域
    const allProductSections = document.querySelectorAll('.product-section');
    allProductSections.forEach(section => {
        section.style.display = 'none';
    });

    // 显示目标产品区域
    const targetElement = document.querySelector(targetId);
    if (targetElement) {
        targetElement.style.display = 'block';

        // 筛选该区域内的产品
        const productItems = targetElement.querySelectorAll('.product-item');
        let hasVisibleProducts = false;

        productItems.forEach(item => {
            const brand = item.getAttribute('data-brand');
            if (brand && selectedBrands.includes(brand)) {
                item.classList.remove('filtered');
                hasVisibleProducts = true;
            } else {
                item.classList.add('filtered');
            }
        });

        // 如果该分类下没有匹配的品牌产品，显示提示
        if (!hasVisibleProducts) {
            showNoCategoryProductsMessage(selectedBrands, targetElement);
        } else {
            hideNoCategoryProductsMessage(targetElement);
        }

        // 滚动到目标位置
        scrollToTarget(targetElement);

        // 更新URL哈希值
        history.pushState(null, null, targetId);
    }
}

// 显示指定分类的所有产品
function showCategoryProducts(targetId) {
    // 隐藏欢迎信息和统一产品容器
    const welcomeSection = document.getElementById('product-welcome');
    const unifiedContainer = document.getElementById('unified-products-container');

    if (welcomeSection) {
        welcomeSection.style.display = 'none';
    }
    if (unifiedContainer) {
        unifiedContainer.style.display = 'none';
    }

    // 隐藏所有产品区域
    const allProductSections = document.querySelectorAll('.product-section');
    allProductSections.forEach(section => {
        section.style.display = 'none';
    });

    // 显示目标产品区域
    const targetElement = document.querySelector(targetId);
    if (targetElement) {
        targetElement.style.display = 'block';

        // 移除所有产品的筛选状态
        const productItems = targetElement.querySelectorAll('.product-item');
        productItems.forEach(item => {
            item.classList.remove('filtered');
        });

        // 隐藏分类无产品提示
        hideNoCategoryProductsMessage(targetElement);

        // 滚动到目标位置
        scrollToTarget(targetElement);

        // 更新URL哈希值
        history.pushState(null, null, targetId);
    }
}

// 滚动到目标位置
function scrollToTarget(targetElement) {
    const offset = 80;
    const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - offset;

    window.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
    });
}

// 显示分类内无产品提示
function showNoCategoryProductsMessage(selectedBrands, targetElement) {
    hideNoCategoryProductsMessage(targetElement);

    const brandNames = selectedBrands.map(brand => {
        const brandMap = {
            'uni-t': '优利德',
            'tektronix': '泰克',
            'maiknow': '脉知技术',
            'hioki': '日置',
            'fluke': '福禄克',
            'cybertek': '知用电子',
            'zhide': '致德测控',
            'zhixin': '致新精密'
        };
        return brandMap[brand] || brand;
    }).join('、');

    const categoryTitle = targetElement.querySelector('h2').textContent;

    const message = document.createElement('div');
    message.className = 'no-category-products-message';
    message.innerHTML = `
        <div class="no-products-content">
            <i class="fas fa-filter"></i>
            <h3>该分类下暂无 ${brandNames} 品牌产品</h3>
            <p>当前分类：${categoryTitle}</p>
            <div class="message-actions">
                <button class="btn btn-outline" onclick="showAllCategoryProducts('${targetElement.id}')">查看该分类所有产品</button>
                <button class="btn btn-primary" onclick="showAllBrandProducts()">查看该品牌所有产品</button>
            </div>
        </div>
    `;

    const productList = targetElement.querySelector('.product-list');
    if (productList) {
        productList.appendChild(message);
    }
}

// 隐藏分类内无产品提示
function hideNoCategoryProductsMessage(targetElement) {
    const existingMessage = targetElement.querySelector('.no-category-products-message');
    if (existingMessage) {
        existingMessage.remove();
    }
}

// 显示该分类的所有产品（从提示消息中调用）
function showAllCategoryProducts(categoryId) {
    const targetElement = document.getElementById(categoryId);
    if (targetElement) {
        const productItems = targetElement.querySelectorAll('.product-item');
        productItems.forEach(item => {
            item.classList.remove('filtered');
        });
        hideNoCategoryProductsMessage(targetElement);
    }
}

// 显示该品牌的所有产品（从提示消息中调用）
function showAllBrandProducts() {
    const selectedBrands = getSelectedBrands();
    if (selectedBrands.length > 0) {
        showBrandProducts(selectedBrands);
    }
}

// 处理URL哈希值
function handleUrlHash() {
    // 获取URL中的哈希值
    const hash = window.location.hash;

    if (hash) {
        // 延迟执行以确保页面已完全加载
        setTimeout(() => {
            const targetElement = document.querySelector(hash);
            if (targetElement) {
                // 隐藏欢迎信息和统一产品容器
                const welcomeSection = document.getElementById('product-welcome');
                const unifiedContainer = document.getElementById('unified-products-container');

                if (welcomeSection) {
                    welcomeSection.style.display = 'none';
                }
                if (unifiedContainer) {
                    unifiedContainer.style.display = 'none';
                }

                // 隐藏所有产品区域
                const allProductSections = document.querySelectorAll('.product-section');
                allProductSections.forEach(section => {
                    section.style.display = 'none';
                });

                // 显示目标产品区域
                targetElement.style.display = 'block';
                
                // 滚动到目标位置
                const offset = 80;
                const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - offset;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
                
                // 如果目标是产品区域，展开对应的分类菜单并高亮显示
                const categoryId = hash.split('-')[0].substring(1); // 提取类别ID
                const categoryMenu = document.querySelector(`.sidebar-category [href*="${categoryId}"]`);
                if (categoryMenu) {
                    // 高亮显示当前选中的菜单项
                    document.querySelectorAll('.category-submenu a').forEach(item => {
                        item.classList.remove('active');
                    });
                    categoryMenu.classList.add('active');
                    
                    // 确保对应的分类菜单是展开状态
                    const categoryParent = categoryMenu.closest('.sidebar-category');
                    if (categoryParent) {
                        categoryParent.classList.remove('collapsed');
                    }
                }
            }
        }, 300);
    }
}

// 响应式布局处理
function handleResponsiveLayout() {
    const sidebar = document.querySelector('.products-sidebar');
    
    // 在移动设备上添加一个切换按钮
    if (window.innerWidth <= 992 && sidebar) {
        // 检查是否已添加切换按钮
        if (!document.querySelector('.sidebar-toggle')) {
            const toggleBtn = document.createElement('button');
            toggleBtn.className = 'sidebar-toggle';
            toggleBtn.innerHTML = '<i class="fas fa-bars"></i> 产品分类';
            
            sidebar.parentNode.insertBefore(toggleBtn, sidebar);
            
            toggleBtn.addEventListener('click', function() {
                sidebar.classList.toggle('active');
                this.classList.toggle('active');
            });
        }
    }
}

// 窗口大小改变时重新处理响应式布局
window.addEventListener('resize', handleResponsiveLayout);

// 初始化时也执行一次
document.addEventListener('DOMContentLoaded', handleResponsiveLayout);

// ==================== 现代化交互效果函数 ====================

// 鼠标跟随效果
function initMouseFollower() {
    // 检查是否已经存在光标元素
    if (document.querySelector('.custom-cursor')) return;

    const cursor = document.createElement('div');
    cursor.className = 'custom-cursor';
    document.body.appendChild(cursor);

    let mouseX = 0, mouseY = 0;
    let cursorX = 0, cursorY = 0;

    document.addEventListener('mousemove', (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;
    });

    function animateCursor() {
        cursorX += (mouseX - cursorX) * 0.1;
        cursorY += (mouseY - cursorY) * 0.1;
        cursor.style.left = cursorX + 'px';
        cursor.style.top = cursorY + 'px';
        requestAnimationFrame(animateCursor);
    }
    animateCursor();

    // 悬停效果
    document.querySelectorAll('a, button, .btn, .product-item').forEach(el => {
        el.addEventListener('mouseenter', () => {
            cursor.classList.add('cursor-hover');
        });
        el.addEventListener('mouseleave', () => {
            cursor.classList.remove('cursor-hover');
        });
    });
}

// 增强产品卡片交互
function initEnhancedProductCards() {
    const cards = document.querySelectorAll('.product-item');

    cards.forEach(card => {
        card.addEventListener('mouseenter', function(e) {
            this.style.transform = 'translateY(-10px) rotateX(5deg) rotateY(5deg)';
            this.style.boxShadow = '0 20px 40px rgba(0,0,0,0.15)';

            // 添加光影效果
            const light = document.createElement('div');
            light.className = 'card-light';
            this.appendChild(light);
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';

            const light = this.querySelector('.card-light');
            if (light) light.remove();
        });

        card.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const centerX = rect.width / 2;
            const centerY = rect.height / 2;

            const rotateX = (y - centerY) / 15;
            const rotateY = (centerX - x) / 15;

            this.style.transform = `translateY(-10px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
        });
    });
}

// 磁性按钮效果
function initMagneticButtons() {
    const buttons = document.querySelectorAll('.btn, .btn-more, .btn-primary, .btn-outline, button');

    buttons.forEach(button => {
        button.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left - rect.width / 2;
            const y = e.clientY - rect.top - rect.height / 2;

            this.style.transform = `translate(${x * 0.1}px, ${y * 0.1}px)`;
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
    });
}

// 滚动动画
function initScrollAnimations() {
    const animatedElements = document.querySelectorAll('.product-item, .sidebar-category, .product-section h2');

    const scrollObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.classList.add('animate-slide-up');
                }, index * 50);
                scrollObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1 });

    animatedElements.forEach(el => {
        scrollObserver.observe(el);
    });
}

// 工具函数：节流
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}