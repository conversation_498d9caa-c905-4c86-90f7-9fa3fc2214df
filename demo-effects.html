<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交互效果演示 - 广州迅屿科技</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .demo-section {
            padding: 4rem 0;
            margin: 2rem 0;
            border-radius: 20px;
            background: linear-gradient(135deg, var(--gray-50), var(--gray-100));
        }
        
        .demo-title {
            text-align: center;
            font-size: 2.5rem;
            color: var(--primary-700);
            margin-bottom: 3rem;
            position: relative;
        }
        
        .demo-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: var(--gradient-primary);
            border-radius: 2px;
        }
        
        .effect-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .effect-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: var(--shadow-lg);
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .effect-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-2xl);
        }
        
        .effect-demo {
            height: 200px;
            background: var(--gradient-primary);
            border-radius: 10px;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            position: relative;
            overflow: hidden;
        }
        
        .particle-demo {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
        }
        
        .magnetic-demo {
            cursor: pointer;
            transition: all 0.1s ease;
        }
        
        .counter-demo {
            font-size: 3rem;
            font-weight: bold;
            color: var(--primary-600);
        }
        
        .typewriter-demo {
            border-right: 2px solid white;
            animation: blink-caret 1s step-end infinite;
        }

        /* 高级效果样式 */
        .cube-container {
            perspective: 1000px;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .cube {
            width: 100px;
            height: 100px;
            position: relative;
            transform-style: preserve-3d;
            animation: rotateCube 10s infinite linear;
        }

        .face {
            position: absolute;
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, var(--primary-500), var(--primary-700));
            border: 2px solid var(--primary-300);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            opacity: 0.9;
        }

        .front { transform: rotateY(0deg) translateZ(50px); }
        .back { transform: rotateY(180deg) translateZ(50px); }
        .right { transform: rotateY(90deg) translateZ(50px); }
        .left { transform: rotateY(-90deg) translateZ(50px); }
        .top { transform: rotateX(90deg) translateZ(50px); }
        .bottom { transform: rotateX(-90deg) translateZ(50px); }

        @keyframes rotateCube {
            0% { transform: rotateX(0deg) rotateY(0deg); }
            100% { transform: rotateX(360deg) rotateY(360deg); }
        }

        /* 视差层效果 */
        .parallax-container {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .parallax-layer {
            position: absolute;
            width: 120%;
            height: 120%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            border-radius: 10px;
        }

        .parallax-layer:nth-child(1) {
            background: linear-gradient(45deg, rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.3));
            animation: parallaxMove1 8s ease-in-out infinite;
        }

        .parallax-layer:nth-child(2) {
            background: linear-gradient(45deg, rgba(16, 185, 129, 0.4), rgba(59, 130, 246, 0.4));
            animation: parallaxMove2 6s ease-in-out infinite;
        }

        .parallax-layer:nth-child(3) {
            background: linear-gradient(45deg, rgba(245, 158, 11, 0.5), rgba(239, 68, 68, 0.5));
            animation: parallaxMove3 4s ease-in-out infinite;
        }

        @keyframes parallaxMove1 {
            0%, 100% { transform: translateX(-10%) translateY(-10%); }
            50% { transform: translateX(10%) translateY(10%); }
        }

        @keyframes parallaxMove2 {
            0%, 100% { transform: translateX(5%) translateY(10%); }
            50% { transform: translateX(-5%) translateY(-10%); }
        }

        @keyframes parallaxMove3 {
            0%, 100% { transform: translateX(10%) translateY(-5%); }
            50% { transform: translateX(-10%) translateY(5%); }
        }

        /* 进度环样式 */
        .rings-container {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
        }

        .progress-ring {
            position: relative;
            width: 120px;
            height: 120px;
        }

        .progress-ring svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }

        .progress-circle {
            stroke-dasharray: 283;
            stroke-dashoffset: 283;
            transition: stroke-dashoffset 2s ease-in-out;
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-600);
        }

        /* 波浪效果 */
        .wave-container {
            position: relative;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, var(--primary-600), var(--primary-800));
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .wave {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.3));
            border-radius: 50%;
            animation: waveMove 3s ease-in-out infinite;
        }

        .wave:nth-child(2) {
            animation-delay: 1s;
            opacity: 0.7;
        }

        .wave:nth-child(3) {
            animation-delay: 2s;
            opacity: 0.5;
        }

        .wave-text {
            position: relative;
            z-index: 10;
            color: white;
            font-weight: bold;
        }

        @keyframes waveMove {
            0% { transform: translateY(100%) scale(0); }
            50% { transform: translateY(0%) scale(1); }
            100% { transform: translateY(-100%) scale(0); }
        }

        /* 磁场效果 */
        .field-container {
            position: relative;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
        }

        .magnetic-particle {
            position: absolute;
            width: 10px;
            height: 10px;
            background: var(--primary-500);
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .magnetic-particle:nth-child(1) { top: 20%; left: 20%; }
        .magnetic-particle:nth-child(2) { top: 60%; left: 80%; }
        .magnetic-particle:nth-child(3) { top: 80%; left: 30%; }
        .magnetic-particle:nth-child(4) { top: 40%; left: 70%; }
        .magnetic-particle:nth-child(5) { top: 30%; left: 50%; }

        /* 涟漪效果 */
        .ripple-container {
            position: relative;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, var(--primary-500), var(--primary-700));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            cursor: pointer;
            overflow: hidden;
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: rippleAnimation 0.6s linear;
        }

        @keyframes rippleAnimation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <nav style="padding: 1rem 0;">
                <a href="index.html" style="color: var(--primary-600); text-decoration: none; font-size: 1.2rem;">
                    <i class="fas fa-arrow-left"></i> 返回首页
                </a>
            </nav>
        </div>
    </header>

    <main>
        <div class="container">
            <section class="demo-section">
                <h1 class="demo-title">🎨 交互效果演示</h1>
                <p style="text-align: center; font-size: 1.2rem; color: var(--gray-600); max-width: 600px; margin: 0 auto;">
                    体验我们为首页添加的各种现代化交互效果，包括粒子背景、鼠标跟随、3D卡片悬停等精彩功能。
                </p>
                
                <div class="effect-grid">
                    <!-- 粒子背景效果 -->
                    <div class="effect-card">
                        <div class="effect-demo particle-demo" id="particle-demo">
                            <canvas id="demo-canvas" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;"></canvas>
                            <span style="position: relative; z-index: 2;">粒子背景效果</span>
                        </div>
                        <h3>动态粒子背景</h3>
                        <p>鼠标移动时粒子会产生交互反应，营造科技感氛围</p>
                    </div>

                    <!-- 3D卡片效果 -->
                    <div class="effect-card enhanced-hover light-trail">
                        <div class="effect-demo">
                            3D悬停效果
                        </div>
                        <h3>3D卡片悬停</h3>
                        <p>鼠标悬停时卡片产生3D旋转和光影效果</p>
                    </div>

                    <!-- 磁性按钮效果 -->
                    <div class="effect-card">
                        <div class="effect-demo magnetic-demo" id="magnetic-demo">
                            磁性吸附效果
                        </div>
                        <h3>磁性按钮</h3>
                        <p>鼠标靠近时按钮会被"吸引"向鼠标方向移动</p>
                    </div>

                    <!-- 数字计数效果 -->
                    <div class="effect-card">
                        <div class="effect-demo">
                            <div class="counter-demo" id="counter-demo">0</div>
                        </div>
                        <h3>数字计数动画</h3>
                        <p>滚动到视图时数字从0开始递增到目标值</p>
                    </div>

                    <!-- 打字机效果 -->
                    <div class="effect-card">
                        <div class="effect-demo">
                            <div class="typewriter-demo" id="typewriter-demo"></div>
                        </div>
                        <h3>打字机效果</h3>
                        <p>文字逐字显示，模拟真实打字过程</p>
                    </div>

                    <!-- 光线追踪效果 -->
                    <div class="effect-card light-trail">
                        <div class="effect-demo">
                            光线追踪效果
                        </div>
                        <h3>光线追踪</h3>
                        <p>鼠标悬停时产生光线扫过的视觉效果</p>
                    </div>
                </div>
            </section>

            <!-- 高级粒子系统效果 -->
            <section class="demo-section">
                <h2 class="demo-title">🌌 高级粒子系统</h2>
                <div class="effect-grid">
                    <div class="effect-card">
                        <div class="effect-demo" id="dna-helix">
                            <canvas id="dna-canvas"></canvas>
                            <span style="position: relative; z-index: 2; color: white;">DNA螺旋结构</span>
                        </div>
                        <h3>DNA螺旋粒子系统</h3>
                        <p>模拟DNA双螺旋结构的3D粒子动画，适用于生物科技、医疗健康类网站</p>
                    </div>

                    <div class="effect-card">
                        <div class="effect-demo" id="network-graph">
                            <canvas id="network-canvas"></canvas>
                            <span style="position: relative; z-index: 2; color: white;">网络连接图</span>
                        </div>
                        <h3>动态网络连接图</h3>
                        <p>展示节点间动态连接的网络图，适用于科技公司、数据分析平台</p>
                    </div>

                    <div class="effect-card">
                        <div class="effect-demo" id="galaxy-effect">
                            <canvas id="galaxy-canvas"></canvas>
                            <span style="position: relative; z-index: 2; color: white;">星系旋转效果</span>
                        </div>
                        <h3>星系旋转粒子系统</h3>
                        <p>模拟星系旋转的粒子效果，适用于科技、天文、创新类企业网站</p>
                    </div>
                </div>
            </section>

            <!-- 3D变换和视差效果 -->
            <section class="demo-section">
                <h2 class="demo-title">🎯 3D变换与视差</h2>
                <div class="effect-grid">
                    <div class="effect-card">
                        <div class="effect-demo" id="cube-3d">
                            <div class="cube-container">
                                <div class="cube">
                                    <div class="face front">前</div>
                                    <div class="face back">后</div>
                                    <div class="face right">右</div>
                                    <div class="face left">左</div>
                                    <div class="face top">上</div>
                                    <div class="face bottom">下</div>
                                </div>
                            </div>
                        </div>
                        <h3>3D立方体旋转</h3>
                        <p>鼠标控制的3D立方体，可用于产品展示、品牌logo展示</p>
                    </div>

                    <div class="effect-card">
                        <div class="effect-demo" id="parallax-layers">
                            <div class="parallax-container">
                                <div class="parallax-layer" data-speed="0.2">背景层</div>
                                <div class="parallax-layer" data-speed="0.5">中间层</div>
                                <div class="parallax-layer" data-speed="0.8">前景层</div>
                            </div>
                        </div>
                        <h3>多层视差滚动</h3>
                        <p>多层次的视差滚动效果，增强页面深度感和沉浸体验</p>
                    </div>

                    <div class="effect-card">
                        <div class="effect-demo" id="morphing-shapes">
                            <svg viewBox="0 0 200 200" style="width: 100%; height: 100%;">
                                <path id="morph-path" fill="url(#morphGradient)" />
                                <defs>
                                    <linearGradient id="morphGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#3b82f6"/>
                                        <stop offset="100%" style="stop-color:#9333ea"/>
                                    </linearGradient>
                                </defs>
                            </svg>
                        </div>
                        <h3>形状变形动画</h3>
                        <p>SVG路径的平滑变形动画，适用于logo动画、图标过渡效果</p>
                    </div>
                </div>
            </section>

            <!-- 数据可视化动画 -->
            <section class="demo-section">
                <h2 class="demo-title">📊 数据可视化动画</h2>
                <div class="effect-grid">
                    <div class="effect-card">
                        <div class="effect-demo" id="animated-chart">
                            <canvas id="chart-canvas"></canvas>
                        </div>
                        <h3>动态图表动画</h3>
                        <p>数据驱动的图表动画，适用于数据展示、业绩报告页面</p>
                    </div>

                    <div class="effect-card">
                        <div class="effect-demo" id="progress-rings">
                            <div class="rings-container">
                                <div class="progress-ring" data-progress="75">
                                    <svg viewBox="0 0 100 100">
                                        <circle cx="50" cy="50" r="45" fill="none" stroke="#e5e7eb" stroke-width="8"/>
                                        <circle cx="50" cy="50" r="45" fill="none" stroke="#3b82f6" stroke-width="8"
                                                stroke-linecap="round" class="progress-circle"/>
                                    </svg>
                                    <span class="progress-text">75%</span>
                                </div>
                            </div>
                        </div>
                        <h3>环形进度动画</h3>
                        <p>动态环形进度条，适用于技能展示、项目进度、统计数据</p>
                    </div>

                    <div class="effect-card">
                        <div class="effect-demo" id="wave-animation">
                            <div class="wave-container">
                                <div class="wave"></div>
                                <div class="wave"></div>
                                <div class="wave"></div>
                                <span class="wave-text">数据流动</span>
                            </div>
                        </div>
                        <h3>波浪数据流动画</h3>
                        <p>模拟数据流动的波浪效果，适用于数据传输、流程展示</p>
                    </div>
                </div>
            </section>

            <!-- 高级鼠标交互效果 -->
            <section class="demo-section">
                <h2 class="demo-title">🖱️ 高级鼠标交互</h2>
                <div class="effect-grid">
                    <div class="effect-card">
                        <div class="effect-demo" id="magnetic-field">
                            <div class="field-container">
                                <div class="magnetic-particle"></div>
                                <div class="magnetic-particle"></div>
                                <div class="magnetic-particle"></div>
                                <div class="magnetic-particle"></div>
                                <div class="magnetic-particle"></div>
                            </div>
                        </div>
                        <h3>磁场效应</h3>
                        <p>粒子被鼠标"磁化"的效果，创造独特的交互体验</p>
                    </div>

                    <div class="effect-card">
                        <div class="effect-demo" id="gravity-effect">
                            <canvas id="gravity-canvas"></canvas>
                        </div>
                        <h3>重力场效果</h3>
                        <p>鼠标产生重力场，吸引周围的粒子，适用于科技感强的网站</p>
                    </div>

                    <div class="effect-card">
                        <div class="effect-demo" id="ripple-effect">
                            <div class="ripple-container">
                                <span>点击产生涟漪</span>
                            </div>
                        </div>
                        <h3>涟漪扩散效果</h3>
                        <p>点击产生的涟漪扩散动画，增强用户交互反馈</p>
                    </div>
                </div>
            </section>

            <section class="demo-section">
                <h2 class="demo-title">🚀 性能优化特性</h2>
                <div class="effect-grid">
                    <div class="effect-card">
                        <div class="effect-demo">
                            <i class="fas fa-tachometer-alt" style="font-size: 3rem;"></i>
                        </div>
                        <h3>GPU加速</h3>
                        <p>使用CSS transform3d和will-change属性启用硬件加速</p>
                    </div>

                    <div class="effect-card">
                        <div class="effect-demo">
                            <i class="fas fa-mobile-alt" style="font-size: 3rem;"></i>
                        </div>
                        <h3>响应式适配</h3>
                        <p>在移动设备上自动禁用复杂动画以保证性能</p>
                    </div>

                    <div class="effect-card">
                        <div class="effect-demo">
                            <i class="fas fa-universal-access" style="font-size: 3rem;"></i>
                        </div>
                        <h3>无障碍支持</h3>
                        <p>支持prefers-reduced-motion，尊重用户的动画偏好</p>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <script src="js/common.js"></script>
    <script>
        // 演示页面特定的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化演示粒子效果
            initDemoParticles();

            // 初始化磁性效果演示
            initMagneticDemo();

            // 初始化计数器演示
            initCounterDemo();

            // 初始化打字机演示
            initTypewriterDemo();

            // 初始化高级效果
            initDNAHelix();
            initNetworkGraph();
            initGalaxyEffect();
            initAnimatedChart();
            initProgressRings();
            initMorphingShapes();
            initMagneticField();
            initGravityEffect();
            initRippleEffect();
        });

        function initDemoParticles() {
            const canvas = document.getElementById('demo-canvas');
            const ctx = canvas.getContext('2d');
            const container = document.getElementById('particle-demo');
            
            canvas.width = container.offsetWidth;
            canvas.height = container.offsetHeight;
            
            const particles = [];
            const particleCount = 20;
            
            for (let i = 0; i < particleCount; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 2,
                    vy: (Math.random() - 0.5) * 2,
                    size: Math.random() * 3 + 1
                });
            }
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                particles.forEach(particle => {
                    particle.x += particle.vx;
                    particle.y += particle.vy;
                    
                    if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                    if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;
                    
                    ctx.beginPath();
                    ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                    ctx.fill();
                });
                
                requestAnimationFrame(animate);
            }
            animate();
        }

        function initMagneticDemo() {
            const magneticElement = document.getElementById('magnetic-demo');
            
            magneticElement.addEventListener('mousemove', function(e) {
                const rect = this.getBoundingClientRect();
                const x = e.clientX - rect.left - rect.width / 2;
                const y = e.clientY - rect.top - rect.height / 2;
                
                this.style.transform = `translate(${x * 0.1}px, ${y * 0.1}px)`;
            });
            
            magneticElement.addEventListener('mouseleave', function() {
                this.style.transform = '';
            });
        }

        function initCounterDemo() {
            const counter = document.getElementById('counter-demo');
            let hasAnimated = false;
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !hasAnimated) {
                        hasAnimated = true;
                        animateCounter(counter, 1000);
                    }
                });
            });
            
            observer.observe(counter);
        }

        function animateCounter(element, target) {
            let current = 0;
            const increment = target / 100;
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current);
            }, 20);
        }

        function initTypewriterDemo() {
            const element = document.getElementById('typewriter-demo');
            const text = '欢迎体验交互效果！';
            let i = 0;
            
            function typeWriter() {
                if (i < text.length) {
                    element.textContent += text.charAt(i);
                    i++;
                    setTimeout(typeWriter, 150);
                } else {
                    setTimeout(() => {
                        element.textContent = '';
                        i = 0;
                        typeWriter();
                    }, 2000);
                }
            }
            
            typeWriter();
        }

        // DNA螺旋效果
        function initDNAHelix() {
            const canvas = document.getElementById('dna-canvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            const particles = [];
            const particleCount = 40;

            for (let i = 0; i < particleCount; i++) {
                particles.push({
                    angle: (i / particleCount) * Math.PI * 4,
                    y: (i / particleCount) * canvas.height,
                    radius: 3,
                    color: i % 2 === 0 ? '#3b82f6' : '#9333ea'
                });
            }

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                particles.forEach(particle => {
                    particle.angle += 0.02;

                    const x1 = canvas.width / 2 + Math.cos(particle.angle) * 50;
                    const x2 = canvas.width / 2 + Math.cos(particle.angle + Math.PI) * 50;

                    // 绘制DNA链
                    ctx.beginPath();
                    ctx.arc(x1, particle.y, particle.radius, 0, Math.PI * 2);
                    ctx.fillStyle = particle.color;
                    ctx.fill();

                    ctx.beginPath();
                    ctx.arc(x2, particle.y, particle.radius, 0, Math.PI * 2);
                    ctx.fillStyle = particle.color;
                    ctx.fill();

                    // 连接线
                    ctx.beginPath();
                    ctx.moveTo(x1, particle.y);
                    ctx.lineTo(x2, particle.y);
                    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
                    ctx.stroke();
                });

                requestAnimationFrame(animate);
            }
            animate();
        }

        // 网络连接图
        function initNetworkGraph() {
            const canvas = document.getElementById('network-canvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            const nodes = [];
            const nodeCount = 8;

            for (let i = 0; i < nodeCount; i++) {
                nodes.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 2,
                    vy: (Math.random() - 0.5) * 2,
                    radius: 5 + Math.random() * 5
                });
            }

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 更新节点位置
                nodes.forEach(node => {
                    node.x += node.vx;
                    node.y += node.vy;

                    if (node.x < 0 || node.x > canvas.width) node.vx *= -1;
                    if (node.y < 0 || node.y > canvas.height) node.vy *= -1;
                });

                // 绘制连接线
                for (let i = 0; i < nodes.length; i++) {
                    for (let j = i + 1; j < nodes.length; j++) {
                        const dx = nodes[i].x - nodes[j].x;
                        const dy = nodes[i].y - nodes[j].y;
                        const distance = Math.sqrt(dx * dx + dy * dy);

                        if (distance < 100) {
                            ctx.beginPath();
                            ctx.moveTo(nodes[i].x, nodes[i].y);
                            ctx.lineTo(nodes[j].x, nodes[j].y);
                            ctx.strokeStyle = `rgba(59, 130, 246, ${1 - distance / 100})`;
                            ctx.stroke();
                        }
                    }
                }

                // 绘制节点
                nodes.forEach(node => {
                    ctx.beginPath();
                    ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
                    ctx.fillStyle = '#3b82f6';
                    ctx.fill();
                    ctx.strokeStyle = '#1e40af';
                    ctx.stroke();
                });

                requestAnimationFrame(animate);
            }
            animate();
        }

        // 星系效果
        function initGalaxyEffect() {
            const canvas = document.getElementById('galaxy-canvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            const stars = [];
            const starCount = 100;
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;

            for (let i = 0; i < starCount; i++) {
                const angle = Math.random() * Math.PI * 2;
                const radius = Math.random() * 80 + 20;
                stars.push({
                    angle: angle,
                    radius: radius,
                    speed: 0.01 + Math.random() * 0.02,
                    size: Math.random() * 2 + 1,
                    opacity: Math.random() * 0.8 + 0.2
                });
            }

            function animate() {
                ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                stars.forEach(star => {
                    star.angle += star.speed;

                    const x = centerX + Math.cos(star.angle) * star.radius;
                    const y = centerY + Math.sin(star.angle) * star.radius;

                    ctx.beginPath();
                    ctx.arc(x, y, star.size, 0, Math.PI * 2);
                    ctx.fillStyle = `rgba(255, 255, 255, ${star.opacity})`;
                    ctx.fill();
                });

                requestAnimationFrame(animate);
            }
            animate();
        }

        // 动态图表
        function initAnimatedChart() {
            const canvas = document.getElementById('chart-canvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            const data = [30, 60, 45, 80, 35, 70, 55];
            const maxValue = Math.max(...data);
            const barWidth = canvas.width / data.length;
            let animationProgress = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                animationProgress += 0.02;
                if (animationProgress > 1) animationProgress = 1;

                data.forEach((value, index) => {
                    const barHeight = (value / maxValue) * canvas.height * 0.8 * animationProgress;
                    const x = index * barWidth + barWidth * 0.1;
                    const y = canvas.height - barHeight;

                    const gradient = ctx.createLinearGradient(0, y, 0, canvas.height);
                    gradient.addColorStop(0, '#3b82f6');
                    gradient.addColorStop(1, '#1e40af');

                    ctx.fillStyle = gradient;
                    ctx.fillRect(x, y, barWidth * 0.8, barHeight);

                    // 数值标签
                    ctx.fillStyle = '#fff';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(value, x + barWidth * 0.4, y - 5);
                });

                if (animationProgress < 1) {
                    requestAnimationFrame(animate);
                }
            }

            // 重复动画
            setInterval(() => {
                animationProgress = 0;
                animate();
            }, 3000);

            animate();
        }

        // 进度环动画
        function initProgressRings() {
            const rings = document.querySelectorAll('.progress-ring');

            rings.forEach(ring => {
                const circle = ring.querySelector('.progress-circle');
                const text = ring.querySelector('.progress-text');
                const progress = parseInt(ring.getAttribute('data-progress'));

                const circumference = 2 * Math.PI * 45;
                const offset = circumference - (progress / 100) * circumference;

                setTimeout(() => {
                    circle.style.strokeDashoffset = offset;

                    // 数字动画
                    let current = 0;
                    const increment = progress / 100;
                    const timer = setInterval(() => {
                        current += increment;
                        if (current >= progress) {
                            current = progress;
                            clearInterval(timer);
                        }
                        text.textContent = Math.floor(current) + '%';
                    }, 20);
                }, 500);
            });
        }

        // 形状变形动画
        function initMorphingShapes() {
            const path = document.getElementById('morph-path');
            if (!path) return;

            const shapes = [
                'M100,50 C150,20 150,80 100,50 C50,80 50,20 100,50',
                'M100,30 L170,100 L100,170 L30,100 Z',
                'M100,20 C140,20 180,60 180,100 C180,140 140,180 100,180 C60,180 20,140 20,100 C20,60 60,20 100,20',
                'M100,30 L130,70 L170,70 L140,100 L150,140 L100,120 L50,140 L60,100 L30,70 L70,70 Z'
            ];

            let currentShape = 0;

            function morphShape() {
                path.setAttribute('d', shapes[currentShape]);
                currentShape = (currentShape + 1) % shapes.length;
            }

            morphShape();
            setInterval(morphShape, 2000);
        }

        // 磁场效果
        function initMagneticField() {
            const container = document.getElementById('magnetic-field');
            if (!container) return;

            const particles = container.querySelectorAll('.magnetic-particle');

            container.addEventListener('mousemove', (e) => {
                const rect = container.getBoundingClientRect();
                const mouseX = e.clientX - rect.left;
                const mouseY = e.clientY - rect.top;

                particles.forEach(particle => {
                    const particleRect = particle.getBoundingClientRect();
                    const particleX = particleRect.left - rect.left + particleRect.width / 2;
                    const particleY = particleRect.top - rect.top + particleRect.height / 2;

                    const dx = mouseX - particleX;
                    const dy = mouseY - particleY;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < 100) {
                        const force = (100 - distance) / 100;
                        const moveX = dx * force * 0.3;
                        const moveY = dy * force * 0.3;

                        particle.style.transform = `translate(${moveX}px, ${moveY}px) scale(${1 + force * 0.5})`;
                    } else {
                        particle.style.transform = '';
                    }
                });
            });

            container.addEventListener('mouseleave', () => {
                particles.forEach(particle => {
                    particle.style.transform = '';
                });
            });
        }

        // 重力效果
        function initGravityEffect() {
            const canvas = document.getElementById('gravity-canvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            const particles = [];
            const particleCount = 30;
            let mouse = { x: canvas.width / 2, y: canvas.height / 2 };

            for (let i = 0; i < particleCount; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: 0,
                    vy: 0,
                    size: Math.random() * 3 + 2
                });
            }

            canvas.addEventListener('mousemove', (e) => {
                const rect = canvas.getBoundingClientRect();
                mouse.x = e.clientX - rect.left;
                mouse.y = e.clientY - rect.top;
            });

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                particles.forEach(particle => {
                    const dx = mouse.x - particle.x;
                    const dy = mouse.y - particle.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance > 0) {
                        const force = 50 / distance;
                        particle.vx += (dx / distance) * force * 0.01;
                        particle.vy += (dy / distance) * force * 0.01;
                    }

                    particle.vx *= 0.99;
                    particle.vy *= 0.99;

                    particle.x += particle.vx;
                    particle.y += particle.vy;

                    // 边界检测
                    if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -0.5;
                    if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -0.5;

                    ctx.beginPath();
                    ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                    ctx.fillStyle = '#3b82f6';
                    ctx.fill();
                });

                requestAnimationFrame(animate);
            }
            animate();
        }

        // 涟漪效果
        function initRippleEffect() {
            const container = document.getElementById('ripple-effect');
            if (!container) return;

            container.addEventListener('click', (e) => {
                const rect = container.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                const ripple = document.createElement('div');
                ripple.className = 'ripple';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';

                container.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        }
    </script>
</body>
</html>
