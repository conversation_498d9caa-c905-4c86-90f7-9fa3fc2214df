:root {
    /* 主色调系统 - 科技蓝色调 */
    --primary-50: #e3f2fd;
    --primary-100: #bbdefb;
    --primary-200: #90caf9;
    --primary-300: #64b5f6;
    --primary-400: #42a5f5;
    --primary-500: #2196f3;
    --primary-600: #1e88e5;
    --primary-700: #1976d2;
    --primary-800: #1565c0;
    --primary-900: #0d47a1;

    /* 辅助色调 - 深蓝科技色 */
    --secondary-50: #e8eaf6;
    --secondary-100: #c5cae9;
    --secondary-200: #9fa8da;
    --secondary-300: #7986cb;
    --secondary-400: #5c6bc0;
    --secondary-500: #3f51b5;
    --secondary-600: #3949ab;
    --secondary-700: #303f9f;
    --secondary-800: #283593;
    --secondary-900: #1a237e;

    /* 中性色系统 */
    --gray-50: #fafafa;
    --gray-100: #f5f5f5;
    --gray-200: #eeeeee;
    --gray-300: #e0e0e0;
    --gray-400: #bdbdbd;
    --gray-500: #9e9e9e;
    --gray-600: #757575;
    --gray-700: #616161;
    --gray-800: #424242;
    --gray-900: #212121;

    /* 功能色彩 */
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --info-color: #2196f3;

    /* 渐变色系统 */
    --gradient-primary: linear-gradient(135deg, var(--primary-600), var(--primary-800));
    --gradient-secondary: linear-gradient(135deg, var(--secondary-600), var(--secondary-800));
    --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
    --gradient-card: linear-gradient(145deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));

    /* 阴影系统 */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

    /* 间距系统 */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;

    /* 圆角系统 */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;

    /* 动画系统 */
    --duration-fast: 0.15s;
    --duration-normal: 0.25s;
    --duration-slow: 0.35s;
    --duration-slower: 0.5s;

    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* 字体系统 */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;

    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* 兼容性变量 */
    --primary-color: var(--primary-600);
    --secondary-color: var(--secondary-700);
    --accent-color: var(--success-color);
    --light-color: var(--gray-50);
    --dark-color: var(--gray-800);
    --section-bg: var(--gray-100);
}

/* 全局重置和基础样式 */
* {
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--gray-800);
    margin: 0;
    padding: 0;
    background-color: var(--gray-50);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-400);
    border-radius: var(--radius-full);
    transition: background var(--duration-normal) var(--ease-out);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-600);
}

/* 基础动画类 */
.animate-fade-in {
    animation: fadeIn var(--duration-slow) var(--ease-out);
}

.animate-slide-up {
    animation: slideUp var(--duration-slow) var(--ease-out);
}

.animate-slide-down {
    animation: slideDown var(--duration-slow) var(--ease-out);
}

.animate-scale-in {
    animation: scaleIn var(--duration-normal) var(--ease-bounce);
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

/* 关键帧动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* 玻璃态效果类 */
.glass-effect {
    background: var(--gradient-glass);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 卡片效果类 */
.card-modern {
    background: var(--gradient-card);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all var(--duration-normal) var(--ease-out);
}

.card-modern:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

/* 按钮基础样式重构 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    text-decoration: none;
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left var(--duration-slow) var(--ease-out);
}

.btn:hover::before {
    left: 100%;
}

/* 按钮变体样式 */
.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--primary-700), var(--primary-900));
}

.btn-secondary {
    background: var(--gradient-secondary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--secondary-700), var(--secondary-900));
}

.btn-outline {
    background: transparent;
    color: var(--primary-600);
    border: 2px solid var(--primary-600);
    box-shadow: var(--shadow-sm);
}

.btn-outline:hover {
    background: var(--primary-600);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-ghost {
    background: rgba(255, 255, 255, 0.1);
    color: var(--gray-700);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.btn-ghost:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 容器样式 */
.container {
    width: 92%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

/* 页面滚动进度条 */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: var(--gradient-primary);
    z-index: 9999;
    transition: width var(--duration-fast) var(--ease-out);
}

/* 回到顶部按钮 */
.back-to-top {
    position: fixed;
    bottom: var(--space-8);
    right: var(--space-8);
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: var(--radius-full);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    box-shadow: var(--shadow-lg);
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all var(--duration-normal) var(--ease-out);
    z-index: 1000;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

/* 加载动画 */
.loading-shimmer {
    background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* 头部样式现代化增强 */
header {
    background: linear-gradient(135deg,
        var(--primary-700) 0%,
        var(--primary-800) 50%,
        var(--secondary-700) 100%);
    color: white;
    box-shadow: var(--shadow-2xl);
    position: relative;
    z-index: 1000;
    /* 移除 overflow: hidden 以允许下拉菜单显示 */
}

header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(ellipse at 15% 30%, rgba(255,255,255,0.15) 0%, transparent 60%),
        radial-gradient(ellipse at 85% 70%, rgba(255,255,255,0.12) 0%, transparent 60%),
        radial-gradient(ellipse at 50% 0%, rgba(33, 150, 243, 0.1) 0%, transparent 70%),
        linear-gradient(135deg, rgba(255,255,255,0.08) 0%, transparent 50%, rgba(255,255,255,0.03) 100%);
    z-index: 1;
}

header::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="%23ffffff" stroke-width="0.3" opacity="0.15"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>'),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><defs><pattern id="dots" width="40" height="40" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="200" height="200" fill="url(%23dots)"/></svg>');
    opacity: 0.4;
    z-index: 1;
}

/* 头部动态光效 */
header .header-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255,255,255,0.1) 30%,
        rgba(255,255,255,0.2) 50%,
        rgba(255,255,255,0.1) 70%,
        transparent 100%);
    z-index: 2;
    animation: headerGlow 8s ease-in-out infinite;
}

@keyframes headerGlow {
    0%, 100% {
        left: -100%;
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    50% {
        left: 100%;
        opacity: 1;
    }
    90% {
        opacity: 0;
    }
}

.header-top {
    padding: var(--space-6) 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    position: relative;
    z-index: 2;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.header-top::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.5), transparent);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--space-8);
    position: relative;
    z-index: 2;
}

/* Logo样式现代化增强 */
.logo {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
    position: relative;
    padding: var(--space-2);
    border-radius: var(--radius-lg);
}

.logo::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
    border-radius: var(--radius-lg);
    opacity: 0;
    transition: opacity var(--duration-normal) var(--ease-out);
}

.logo:hover {
    transform: translateY(-3px);
}

.logo:hover::before {
    opacity: 1;
}

.logo img {
    height: 50px;
    width: auto;
    display: block;
    transition: all var(--duration-normal) var(--ease-out);
    object-fit: contain;
    filter: drop-shadow(0 2px 8px rgba(0,0,0,0.2));
    position: relative;
    z-index: 2;
}

.logo:hover img {
    transform: scale(1.05);
    filter: drop-shadow(0 4px 12px rgba(0,0,0,0.3));
}

.company-slogan {
    font-size: var(--font-size-lg);
    color: rgba(255, 255, 255, 0.9);
    margin-top: var(--space-3);
    letter-spacing: 0.5px;
    font-weight: var(--font-weight-light);
    transition: all var(--duration-normal) var(--ease-out);
    position: relative;
    z-index: 2;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.logo:hover .company-slogan {
    color: rgba(255, 255, 255, 1);
    transform: translateX(3px);
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* 搜索框样式现代化 */
.search-box {
    flex: 1;
    max-width: 500px;
    margin: 0 var(--space-8);
    position: relative;
    display: flex;
    align-items: stretch;
}

.search-box input {
    flex: 1;
    height: 48px;
    padding: 0 var(--space-4);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg) 0 0 var(--radius-lg);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: white;
    font-size: var(--font-size-base);
    transition: all var(--duration-normal) var(--ease-out);
    border-right: none;
    line-height: 48px;
}

.search-box input::placeholder {
    color: rgba(255, 255, 255, 0.7);
    transition: color var(--duration-normal) var(--ease-out);
}

.search-box input:focus {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.search-box input:focus::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.search-box button {
    height: 48px;
    width: 48px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-left: none;
    border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: white;
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
}

.search-box button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.search-box:focus-within button {
    border-color: rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.2);
}

.search-box input:focus {
    border-color: rgba(255, 255, 255, 0.3);
    outline: none;
}

.search-box button {
    width: 50px;
    border: 1px solid var(--accent-color);
    background: var(--accent-color);
    color: white;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

.search-box button i {
    font-size: 1rem;
}

.search-box button:hover {
    background: #3d8b40;
    border-color: #3d8b40;
}

.search-box button:active {
    background: #2d632f;
    border-color: #2d632f;
}

/* 服务热线样式 */
.service-hotline {
    display: flex;
    align-items: center;
    font-size: 1rem;
}

.service-hotline i {
    font-size: 1.5rem;
    margin-right: 0.8rem;
    color: var(--accent-color);
}

/* 联系信息区域增强 */
.hotline {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    padding: var(--space-3) var(--space-4);
    background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255,255,255,0.2);
    transition: all var(--duration-normal) var(--ease-out);
    position: relative;
    z-index: 2;
}

.hotline:hover {
    background: linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.08));
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.hotline-info {
    display: flex;
    flex-direction: column;
}

.hotline-label {
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--space-1);
}

.hotline-number {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: white;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    transition: all var(--duration-normal) var(--ease-out);
}

.hotline:hover .hotline-number {
    color: var(--primary-100);
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* 导航栏样式现代化增强 */
.header-nav {
    padding: var(--space-3) 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.15), rgba(0, 0, 0, 0.08));
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border-top: 1px solid rgba(255, 255, 255, 0.15);
    position: relative;
    z-index: 2;
    /* 确保下拉菜单可以正常显示 */
    overflow: visible;
}

.header-nav::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
}

nav ul {
    display: flex;
    justify-content: center;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: var(--space-6);
    /* 确保下拉菜单可以正常显示 */
    overflow: visible;
}

nav ul li {
    position: relative;
    display: flex;
    align-items: center;
}

nav ul li a {
    color: white;
    text-decoration: none;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-lg);
    transition: all var(--duration-normal) var(--ease-out);
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

nav ul li a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left var(--duration-slow) var(--ease-out);
}

nav ul li a:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

nav ul li a:hover::before {
    left: 100%;
}

/* 下拉菜单样式 */
.nav-item {
    display: flex;
    align-items: center;
    position: relative;
    /* 确保下拉菜单可以正常显示 */
    overflow: visible;
}

.nav-item > a {
    display: flex;
    align-items: center;
    height: 100%;
}

.nav-item > a::after {
    content: '\f107';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-left: 0.5rem;
    transition: transform 0.3s;
    font-size: 0.8em;
    line-height: 1;
}

.nav-item:hover > a::after {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: calc(100% + 5px);
    left: 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.15),
        0 10px 20px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border: 2px solid rgba(255, 255, 255, 0.6);
    padding: var(--space-6);
    min-width: 450px;
    border-radius: var(--radius-xl);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px) scale(0.98);
    transition: all var(--duration-normal) var(--ease-out);
    z-index: 99999;
    pointer-events: none;
    /* 确保下拉菜单不被父容器裁剪 */
    clip-path: none;
    overflow: visible;
}

.dropdown-menu::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.03), rgba(25, 118, 210, 0.05));
    border-radius: var(--radius-xl);
    z-index: -1;
}

/* 两列布局下拉菜单 */
.dropdown-columns {
    display: flex;
    gap: 20px;
}

.dropdown-column {
    min-width: 200px;
}

.nav-item:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
    pointer-events: auto;
}

/* 确保下拉菜单在悬停时保持显示 */
.dropdown-menu:hover {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
    pointer-events: auto;
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    padding: var(--space-3) var(--space-4);
    color: var(--gray-900);
    text-decoration: none;
    transition: all var(--duration-normal) var(--ease-out);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-1);
    position: relative;
    overflow: hidden;
    font-weight: var(--font-weight-medium);
    background: rgba(255, 255, 255, 0.6);
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: var(--shadow-sm);
}

.dropdown-menu a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(33, 150, 243, 0.15), transparent);
    transition: left var(--duration-normal) var(--ease-out);
}

.dropdown-menu a i {
    margin-right: var(--space-3);
    width: 20px;
    text-align: center;
    color: var(--primary-700);
    transition: all var(--duration-normal) var(--ease-out);
    font-weight: bold;
}

.dropdown-menu a:hover {
    background: linear-gradient(135deg, var(--primary-100), var(--primary-50));
    color: var(--primary-800);
    transform: translateX(6px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-300);
}

.dropdown-menu a:hover::before {
    left: 100%;
}

.dropdown-menu a:hover i {
    color: var(--primary-800);
    transform: scale(1.15) rotate(8deg);
}

.dropdown-menu .menu-title {
    font-size: var(--font-size-sm);
    color: var(--gray-900);
    padding: var(--space-2) var(--space-4);
    margin-bottom: var(--space-3);
    border-bottom: 2px solid var(--primary-300);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: var(--font-weight-bold);
    background: linear-gradient(135deg, var(--primary-100), var(--primary-50));
    border-radius: var(--radius-md);
    text-align: center;
    box-shadow: var(--shadow-sm);
}

/* 删除重复的下拉菜单样式 */

/* 产品中心样式现代化 */
.products-section {
    padding: var(--space-20) 0;
    background: linear-gradient(180deg, var(--gray-50), white);
    position: relative;
}

.products-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e0e7ff" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    z-index: 1;
}

.products-section .container {
    position: relative;
    z-index: 2;
}

.products-section h1 {
    text-align: center;
    color: var(--gray-800);
    margin-bottom: var(--space-16);
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    position: relative;
}

.products-section h1::after {
    content: '';
    position: absolute;
    bottom: -var(--space-4);
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    padding: 20px;
}

.category-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    position: relative;
    overflow: hidden;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.category-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: #007bff;
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
}

.category-card:hover:before {
    transform: scaleX(1);
    transform-origin: left;
}

.category-icon {
    width: 80px;
    height: 80px;
    background: #f8f9fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    transition: all 0.3s ease;
}

.category-card:hover .category-icon {
    background: #007bff;
}

.category-icon i {
    font-size: 32px;
    color: #007bff;
    transition: all 0.3s ease;
}

.category-card:hover .category-icon i {
    color: white;
}

.category-card h2 {
    color: #333;
    font-size: 1.5rem;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 15px;
}

.category-card h2:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background: #e9ecef;
}

.category-card ul {
    list-style: none;
    padding: 0;
    margin: 0;
    text-align: left;
}

.category-card li {
    margin: 12px 0;
    transition: all 0.3s ease;
}

.category-card li a {
    color: #666;
    text-decoration: none;
    display: block;
    padding: 8px 15px;
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
    padding-left: 25px;
}

.category-card li a:before {
    content: '•';
    position: absolute;
    left: 10px;
    color: #007bff;
}

.category-card li a:hover {
    color: #007bff;
    background: #f8f9fa;
    transform: translateX(5px);
}

@media (max-width: 768px) {
    .category-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        padding: 15px;
    }

    .category-card {
        padding: 25px;
    }

    .category-icon {
        width: 60px;
        height: 60px;
    }

    .category-icon i {
        font-size: 24px;
    }

    .category-card h2 {
        font-size: 1.3rem;
    }
}

@media (max-width: 480px) {
    .products-section h1 {
        font-size: 2rem;
    }

    .category-grid {
        grid-template-columns: 1fr;
    }
}

/* 服务支持页面样式现代化 */
/* 页面标题区域 */
.page-header {
    padding: var(--space-16) 0 var(--space-12) 0;
    background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.page-header h1 {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--space-4);
    position: relative;
    z-index: 2;
}

.page-header p {
    font-size: var(--font-size-xl);
    opacity: 0.9;
    position: relative;
    z-index: 2;
}

/* 维修服务专区样式 */
.maintenance-showcase {
    padding: var(--space-20) 0;
    position: relative;
}

.maintenance-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--space-16);
    text-align: left;
}

.service-icon-large {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-700));
    border-radius: var(--radius-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--space-8);
    box-shadow: var(--shadow-xl);
    position: relative;
    overflow: hidden;
}

.service-icon-large::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    animation: iconShine 3s ease-in-out infinite;
}

.service-icon-large i {
    font-size: 3rem;
    color: white;
    position: relative;
    z-index: 2;
}

.service-title h2 {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.service-subtitle {
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    line-height: 1.6;
}

.maintenance-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-12);
    align-items: start;
}

.maintenance-info {
    display: flex;
    flex-direction: column;
    gap: var(--space-10);
}

.info-section h3 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--space-6);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.info-section h3 i {
    color: var(--primary-600);
    font-size: var(--font-size-xl);
}

/* 维修流程样式 */
.process-steps {
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
}

.step {
    display: flex;
    align-items: flex-start;
    gap: var(--space-4);
    padding: var(--space-6);
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    border-left: 4px solid var(--primary-500);
    transition: all var(--duration-normal) var(--ease-out);
}

.step:hover {
    transform: translateX(8px);
    box-shadow: var(--shadow-md);
}

.step-number {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.step-content h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.step-content p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* 服务优势网格 */
.advantages-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-6);
}

.advantage-item {
    text-align: center;
    padding: var(--space-6);
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    transition: all var(--duration-normal) var(--ease-out);
}

.advantage-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.advantage-item i {
    font-size: 2.5rem;
    color: var(--primary-600);
    margin-bottom: var(--space-4);
}

.advantage-item h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.advantage-item p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

/* 服务范围样式 */
.service-scope {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-8);
}

.scope-column h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--primary-700);
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-2);
    border-bottom: 2px solid var(--primary-200);
}

.scope-column ul {
    list-style: none;
    padding: 0;
}

.scope-column li {
    padding: var(--space-2) 0;
    color: var(--gray-700);
    position: relative;
    padding-left: var(--space-6);
}

.scope-column li::before {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    color: var(--primary-500);
    position: absolute;
    left: 0;
    top: var(--space-2);
}

/* 维修图片画廊样式 */
.maintenance-gallery {
    position: sticky;
    top: var(--space-8);
}

.maintenance-gallery h3 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--space-6);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.maintenance-gallery h3 i {
    color: var(--primary-600);
    font-size: var(--font-size-xl);
}

.gallery-container {
    background: white;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    border: 1px solid var(--gray-200);
}

.gallery-main {
    position: relative;
    height: 400px;
    overflow: hidden;
}

.gallery-main img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    padding: var(--space-6);
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.gallery-main:hover .image-overlay {
    transform: translateY(0);
}

.image-info h4 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-2);
}

.image-info p {
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

.gallery-thumbnails {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 1px;
    background: var(--gray-200);
}

.thumbnail {
    aspect-ratio: 1;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.thumbnail::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.3);
    opacity: 1;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.thumbnail.active::before,
.thumbnail:hover::before {
    opacity: 0;
}

.thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.thumbnail:hover img {
    transform: scale(1.1);
}

.thumbnail.active {
    box-shadow: inset 0 0 0 3px var(--primary-500);
}

/* 其他服务区域样式 */
.other-services {
    padding: var(--space-20) 0;
    position: relative;
}

.other-services h2 {
    text-align: center;
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-900);
    margin-bottom: var(--space-16);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-8);
}

.service-card {
    background: white;
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    transition: all var(--duration-normal) var(--ease-out);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
}

.service-card .service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-6);
    transition: all 0.3s ease;
}

.service-card:hover .service-icon {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    transform: scale(1.1);
}

.service-card .service-icon i {
    font-size: 2rem;
    color: var(--primary-600);
    transition: color 0.3s ease;
}

.service-card:hover .service-icon i {
    color: white;
}

.service-card h3 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--space-4);
}

.service-card p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--space-6);
}

.service-card ul {
    list-style: none;
    padding: 0;
    margin-bottom: var(--space-8);
}

.service-card li {
    padding: var(--space-2) 0;
    color: var(--gray-700);
    position: relative;
    padding-left: var(--space-6);
}

.service-card li::before {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    color: var(--primary-500);
    position: absolute;
    left: 0;
    top: var(--space-2);
}

.service-action {
    text-align: center;
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--primary-500);
    color: var(--primary-600);
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-weight: var(--font-weight-semibold);
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-outline:hover {
    background: var(--primary-500);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* 动画效果 */
@keyframes iconShine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    100% { transform: translateX(200%) translateY(200%) rotate(45deg); }
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .maintenance-content {
        grid-template-columns: 1fr;
        gap: var(--space-8);
    }

    .maintenance-gallery {
        position: static;
    }

    .services-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .service-scope {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .page-header h1 {
        font-size: var(--font-size-3xl);
    }

    .page-header p {
        font-size: var(--font-size-lg);
    }

    .maintenance-header {
        flex-direction: column;
        text-align: center;
        gap: var(--space-6);
    }

    .service-icon-large {
        margin-right: 0;
        margin-bottom: var(--space-4);
    }

    .service-title h2 {
        font-size: var(--font-size-3xl);
    }

    .service-subtitle {
        font-size: var(--font-size-lg);
    }

    .advantages-grid {
        grid-template-columns: 1fr;
    }

    .service-scope {
        grid-template-columns: 1fr;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .gallery-thumbnails {
        grid-template-columns: repeat(3, 1fr);
    }

    .process-steps {
        gap: var(--space-4);
    }

    .step {
        flex-direction: column;
        text-align: center;
        gap: var(--space-3);
    }

    .step:hover {
        transform: translateY(-4px);
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: var(--space-12) 0 var(--space-8) 0;
    }

    .page-header h1 {
        font-size: var(--font-size-2xl);
    }

    .maintenance-showcase,
    .other-services {
        padding: var(--space-12) 0;
    }

    .service-icon-large {
        width: 80px;
        height: 80px;
    }

    .service-icon-large i {
        font-size: 2rem;
    }

    .service-title h2 {
        font-size: var(--font-size-2xl);
    }

    .gallery-main {
        height: 250px;
    }

    .gallery-thumbnails {
        grid-template-columns: repeat(2, 1fr);
    }

    .service-card {
        padding: var(--space-6);
    }

    .info-section h3 {
        font-size: var(--font-size-xl);
    }
}

.services-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e0e7ff" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    z-index: 1;
}

.services-section .container {
    position: relative;
    z-index: 2;
}

.services-section h2 {
    text-align: center;
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 3rem;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.service-category {
    background: var(--section-bg);
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.service-category::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.service-category:hover::before {
    transform: scaleX(1);
}

.service-category:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.service-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.service-icon i {
    font-size: 2rem;
    color: var(--primary-color);
}

.service-category h3 {
    color: var(--dark-color);
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.service-category p {
    color: #666;
    margin-bottom: 1.5rem;
}

.service-category ul {
    list-style: none;
    padding: 0;
    margin: 0;
    text-align: left;
}

.service-category ul li {
    padding: 0.5rem 0;
    color: #555;
    position: relative;
    padding-left: 1.5rem;
}

.service-category ul li::before {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    color: var(--accent-color);
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.8rem;
}

/* 页脚样式现代化 */
footer {
    background: linear-gradient(135deg, var(--gray-900), var(--gray-800));
    color: white;
    padding: var(--space-20) 0 var(--space-8);
    position: relative;
    overflow: hidden;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23374151" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    z-index: 1;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-8);
    margin-bottom: var(--space-8);
    position: relative;
    z-index: 2;
}

.footer-section {
    position: relative;
    z-index: 2;
}

.footer-section h4 {
    color: white;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-6);
    position: relative;
    padding-bottom: var(--space-3);
}

.footer-section h4::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 60px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
}

.footer-section p {
    margin: var(--space-3) 0;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    transition: color var(--duration-normal) var(--ease-out);
}

.footer-section p:hover {
    color: rgba(255, 255, 255, 0.9);
}

.footer-section p i {
    margin-right: var(--space-3);
    color: var(--primary-400);
    transition: all var(--duration-normal) var(--ease-out);
}

.footer-section p:hover i {
    color: var(--primary-300);
    transform: scale(1.1);
}

.footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-section ul li {
    margin: var(--space-3) 0;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all var(--duration-normal) var(--ease-out);
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) 0;
    position: relative;
}

.footer-section ul li a::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width var(--duration-normal) var(--ease-out);
}

.footer-section ul li a:hover {
    color: white;
    transform: translateX(4px);
}

.footer-section ul li a:hover::before {
    width: 100%;
}

.social-links {
    display: flex;
    gap: var(--space-4);
}

.social-links a {
    color: white;
    font-size: var(--font-size-2xl);
    transition: all var(--duration-normal) var(--ease-out);
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-full);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.social-links a:hover {
    color: white;
    background: var(--gradient-primary);
    transform: translateY(-3px) scale(1.1);
    box-shadow: var(--shadow-lg);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--space-8);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    z-index: 2;
}

.footer-bottom::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 1px;
    background: var(--gradient-primary);
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
    font-size: var(--font-size-sm);
    transition: color var(--duration-normal) var(--ease-out);
}

.footer-bottom p:hover {
    color: rgba(255, 255, 255, 0.8);
}

/* 移动端响应式优化现代化 */
@media (max-width: 768px) {
    .container {
        width: 95%;
        padding: 0 var(--space-4);
    }

    /* 头部导航移动端优化 */
    .header-content {
        flex-direction: column;
        gap: var(--space-4);
    }

    .search-box {
        margin: 0;
        max-width: 100%;
        order: 3;
    }

    .hotline {
        order: 2;
    }

    /* 导航菜单移动端优化 */
    nav ul {
        flex-direction: column;
        gap: var(--space-2);
    }

    nav ul li a {
        padding: var(--space-3);
        text-align: center;
    }

    /* 英雄区域移动端优化 */
    .hero {
        height: 400px;
    }

    .hero-content {
        padding: var(--space-6);
    }

    .hero-content h1 {
        font-size: var(--font-size-3xl);
        text-shadow:
            0 0 15px rgba(255,255,255,0.4),
            0 2px 6px rgba(0,0,0,0.5);
    }

    .hero-content p {
        font-size: var(--font-size-lg);
        text-shadow: 0 1px 3px rgba(0,0,0,0.5);
    }

    .hero-buttons {
        flex-direction: column;
        gap: var(--space-4);
        margin-top: var(--space-4);
    }

    .hero-buttons .btn {
        width: 100%;
        min-width: auto;
        padding: var(--space-4) var(--space-6);
        font-size: var(--font-size-base);
    }

    /* 产品卡片移动端优化 */
    .products-grid {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }

    .product-card img {
        height: 250px;
    }

    /* 解决方案移动端优化 */
    .solutions-grid {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }

    /* 优势区域移动端优化 */
    .advantages-grid {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }

    /* 合作伙伴移动端优化 */
    .partners-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-4);
    }

    /* 产品侧边栏移动端优化 */
    .products-sidebar {
        width: 100%;
        margin-bottom: var(--space-8);
    }

    /* 页脚移动端优化 */
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--space-6);
        text-align: center;
    }

    .social-links {
        justify-content: center;
    }

    .header-content {
        flex-direction: column;
        align-items: center;
        padding: 1rem 0;
    }

    .logo {
        margin-bottom: 1rem;
        text-align: center;
        align-items: center;
    }

    .search-box {
        width: 100%;
        max-width: 100%;
        margin: 1rem 0;
    }

    .service-hotline {
        margin-top: 1rem;
        justify-content: center;
    }

    nav ul {
        flex-direction: column;
        align-items: center;
    }

    nav ul li {
        margin: 0.5rem 0;
        width: 100%;
        text-align: center;
    }

    .dropdown-menu {
        position: static;
        width: 100%;
        box-shadow: none;
        background: rgba(0, 0, 0, 0.1);
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-section {
        margin-bottom: 2rem;
        width: 100%;
    }

    .social-links {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .header-top {
        padding: 1rem 0;
    }

    .logo img {
        height: 40px;
    }

    .company-slogan {
        font-size: 0.9rem;
    }

    .search-box input {
        height: 36px;
        font-size: 0.9rem;
    }

    .hotline-number {
        font-size: 1rem;
    }

    /* 修复移动端导航菜单显示和交互问题 */
    .nav-item > a {
        font-size: 1rem;
        padding: 0.5rem 0.8rem;
        display: block !important;
        color: var(--text-color) !important;
        text-decoration: none !important;
        white-space: nowrap;
        overflow: visible;
        pointer-events: auto !important;
        z-index: 1000;
        position: relative;
    }

    .nav-item > a::after {
        display: inline-block !important;
        margin-left: 0.5rem;
        font-size: 0.8em;
    }

    /* 确保下拉菜单不影响主链接点击 */
    .dropdown-menu {
        pointer-events: auto;
        z-index: 999;
    }

    /* 移动端导航项悬停效果简化 */
    .nav-item:hover > a {
        background: rgba(59, 130, 246, 0.1);
        border-radius: var(--radius-md);
    }

    .footer-bottom {
        padding: 1rem 0;
        font-size: 0.9rem;
    }
}

/* 关于我们样式现代化 */
.about-section {
    padding: var(--space-20) 0;
    background: linear-gradient(180deg, var(--gray-50), white);
    position: relative;
}

.about-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e0e7ff" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    z-index: 1;
}

.about-section .container {
    position: relative;
    z-index: 2;
}

.about-section h2 {
    text-align: center;
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 3rem;
}

.about-content {
    background: white;
    border-radius: 10px;
    padding: 3rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
}

.about-text h3 {
    color: var(--primary-color);
    font-size: 1.8rem;
    margin: 2rem 0 1.5rem;
    position: relative;
    padding-bottom: 0.8rem;
}

.about-text h3:first-child {
    margin-top: 0;
}

.about-text h3::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(to right, var(--primary-color), var(--accent-color));
}

.about-text p {
    color: #555;
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.about-text ul {
    list-style: none;
    padding: 0;
    margin: 0 0 2rem;
}

.about-text ul li {
    padding: 0.8rem 0;
    color: #555;
    display: flex;
    align-items: center;
}

.about-text ul li i {
    color: var(--accent-color);
    margin-right: 1rem;
    font-size: 1.2rem;
}

.partners-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-8);
    margin: var(--space-8) 0;
}

.partner-item {
    background: var(--gradient-card);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: var(--space-6);
    border-radius: var(--radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-md);
    transition: all var(--duration-normal) var(--ease-out);
    text-align: center;
    position: relative;
    perspective: 1000px;
    transform-style: preserve-3d;
    cursor: pointer;
    overflow: hidden;
}

.partner-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.05), rgba(25, 118, 210, 0.1));
    opacity: 0;
    transition: opacity var(--duration-normal) var(--ease-out);
    z-index: 1;
}

.partner-item:hover {
    transform: translateY(-8px) scale(1.05) rotateY(10deg) rotateX(5deg);
    box-shadow: var(--shadow-2xl);
    border-color: rgba(71, 85, 105, 0.4);
    animation: brandCard3D 0.6s ease-out;
}

.partner-item:hover::before {
    opacity: 1;
}

.partner-item img {
    max-width: 100%;
    height: 80px;
    object-fit: contain;
    margin-bottom: var(--space-4);
    transition: all var(--duration-normal) var(--ease-out);
    position: relative;
    z-index: 2;
    filter: grayscale(20%);
}

.partner-item:hover img {
    filter: grayscale(0%);
    transform: scale(1.1);
}

.partner-item h4 {
    color: var(--gray-800);
    font-size: var(--font-size-lg);
    margin: 0;
    font-weight: var(--font-weight-medium);
    position: relative;
    z-index: 2;
    transition: color var(--duration-normal) var(--ease-out);
}

.partner-item:hover h4 {
    color: var(--primary-700);
}

/* 品牌卡片3D旋转动画 */
@keyframes brandCard3D {
    0% {
        transform: translateY(0) scale(1) rotateY(0deg) rotateX(0deg);
    }
    50% {
        transform: translateY(-12px) scale(1.08) rotateY(15deg) rotateX(8deg);
    }
    100% {
        transform: translateY(-8px) scale(1.05) rotateY(10deg) rotateX(5deg);
    }
}

@media (max-width: 1200px) {
    .partners-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .partners-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .partner-item img {
        height: 60px;
    }
}

/* 小屏幕设备优化 */
@media (max-width: 480px) {
    /* 基础布局 */
    .container {
        width: 98%;
        padding: 0 var(--space-2);
    }

    /* 头部优化 */
    .header-top {
        padding: var(--space-4) 0;
    }

    .logo img {
        height: 40px;
    }

    .company-slogan {
        font-size: var(--font-size-sm);
        margin-top: var(--space-2);
    }

    .search-box input,
    .search-box button {
        height: 40px;
    }

    .hotline-number {
        font-size: var(--font-size-sm);
    }

    /* 英雄区域 */
    .hero {
        height: 350px;
    }

    .hero-content {
        padding: var(--space-4);
    }

    .hero-content h1 {
        font-size: var(--font-size-2xl);
        margin-bottom: var(--space-4);
        text-shadow:
            0 0 10px rgba(255,255,255,0.3),
            0 1px 4px rgba(0,0,0,0.6);
    }

    .hero-content p {
        font-size: var(--font-size-base);
        margin-bottom: var(--space-6);
        text-shadow: 0 1px 2px rgba(0,0,0,0.6);
    }

    .hero-buttons {
        gap: var(--space-3);
        margin-top: var(--space-4);
    }

    .hero-buttons .btn {
        padding: var(--space-3) var(--space-4);
        font-size: var(--font-size-sm);
        min-width: auto;
    }

    /* 产品卡片 */
    .product-card {
        margin: 0 var(--space-2);
    }

    .product-card img {
        height: 200px;
        padding: var(--space-4);
    }

    /* 解决方案卡片 */
    .solution-card {
        padding: var(--space-6);
    }

    .solution-icon {
        width: 60px;
        height: 60px;
    }

    .solution-icon i {
        font-size: var(--font-size-2xl);
    }

    /* 优势项目 */
    .advantage-item {
        padding: var(--space-6);
    }

    .advantage-item i {
        font-size: var(--font-size-4xl);
    }

    /* 合作伙伴 */
    .partners-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    .partner-item {
        padding: var(--space-4);
    }

    .partner-item img {
        height: 60px;
    }

    /* 页脚 */
    .footer-section h4 {
        font-size: var(--font-size-lg);
    }

    .social-links a {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-lg);
    }

    /* 按钮优化 */
    .btn {
        padding: var(--space-3) var(--space-5);
        font-size: var(--font-size-sm);
    }

    /* 回到顶部按钮 */
    .back-to-top {
        width: 45px;
        height: 45px;
        bottom: var(--space-6);
        right: var(--space-6);
    }
}

/* 大屏幕设备优化 */
@media (min-width: 1200px) {
    .container {
        max-width: 1600px;
    }

    /* 英雄区域 */
    .hero {
        height: 700px;
    }

    .hero-content h1 {
        font-size: var(--font-size-5xl);
    }

    .hero-content p {
        font-size: var(--font-size-2xl);
    }

    /* 产品网格 */
    .products-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--space-10);
    }

    /* 解决方案网格 */
    .solutions-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    /* 优势网格 */
    .advantages-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    /* 合作伙伴网格 */
    .partners-grid {
        grid-template-columns: repeat(6, 1fr);
    }

    /* 产品侧边栏 */
    .products-sidebar {
        width: 350px;
    }
}

/* 超大屏幕设备优化 */
@media (min-width: 1600px) {
    .container {
        max-width: 1800px;
    }

    /* 英雄区域 */
    .hero {
        height: 800px;
    }

    /* 产品网格 */
    .products-grid {
        grid-template-columns: repeat(5, 1fr);
    }

    /* 合作伙伴网格 */
    .partners-grid {
        grid-template-columns: repeat(8, 1fr);
    }
}

/* 联系我们页面样式现代化 */
.contact-page {
    background: linear-gradient(180deg, var(--gray-50), white);
    padding-bottom: var(--space-16);
    position: relative;
}

.contact-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e0e7ff" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    z-index: 1;
}

.contact-page .container {
    position: relative;
    z-index: 2;
}

.contact-banner {
    background: linear-gradient(rgba(0, 86, 179, 0.9), rgba(0, 51, 102, 0.9));
    padding: 40px 0;
    text-align: center;
    color: white;
    margin-bottom: 40px;
}

.contact-banner h1 {
    font-size: 2.2rem;
    margin-bottom: 12px;
    animation: fadeInDown 0.8s ease;
}

.contact-banner p {
    font-size: 1.1rem;
    opacity: 0.9;
    animation: fadeInUp 0.8s ease 0.2s;
    animation-fill-mode: forwards;
}

.contact-cards {
    padding: 0 0 40px;
}

.cards-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-top: -20px;
}

.contact-card {
    background: white;
    padding: 25px 15px;
    border-radius: 12px;
    text-align: center;
    transition: all 0.4s ease;
    box-shadow: 0 6px 20px rgba(0,0,0,0.08);
    position: relative;
    overflow: hidden;
    min-height: 180px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.contact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, #007bff, #00a6ff);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
}

.contact-card:hover::before {
    transform: scaleX(1);
}

.contact-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.12);
}

.card-icon {
    width: 60px;
    height: 60px;
    background: #f0f7ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    transition: all 0.4s ease;
    position: relative;
    z-index: 1;
}

.contact-card:hover .card-icon {
    background: #007bff;
    transform: rotateY(180deg);
}

.card-icon i {
    font-size: 24px;
    color: #007bff;
    transition: all 0.4s ease;
}

.contact-card:hover .card-icon i {
    color: white;
    transform: rotateY(-180deg);
}

.contact-card h3 {
    color: #333;
    font-size: 1.3rem;
    margin-bottom: 12px;
    position: relative;
    padding-bottom: 10px;
}

.contact-card h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 35px;
    height: 2px;
    background: #007bff;
    transition: width 0.3s ease;
}

.contact-card:hover h3::after {
    width: 50px;
}

.contact-card p {
    color: #666;
    line-height: 1.4;
    margin-bottom: 12px;
    font-size: 0.95rem;
}

.contact-card .highlight {
    color: #007bff;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 10px 0;
}

.contact-card .sub-text {
    color: #888;
    font-size: 0.85rem;
    font-style: italic;
}

.contact-card .time-info {
    margin: 6px 0;
    color: #555;
}

.contact-card .time-info span {
    color: #007bff;
    font-weight: 500;
}

.card-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 18px;
    background: #f0f7ff;
    color: #007bff;
    border-radius: 18px;
    text-decoration: none;
    transition: all 0.3s ease;
    margin-top: 12px;
    font-weight: 500;
    font-size: 0.9rem;
}

.card-btn:hover {
    background: #007bff;
    color: white;
    transform: translateX(5px);
}

.qr-code {
    margin-top: 12px;
    position: relative;
}

.qr-code img {
    width: 130px;
    height: 130px;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: transform 0.4s ease;
    margin-bottom: 8px;
}

.qr-code img:hover {
    transform: scale(1.05);
}

/* 地址区域样式 */
.address-section {
    padding: 40px 0;
    background: white;
}

.address-card {
    background: linear-gradient(145deg, #ffffff, #f5f8ff);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: flex-start;
    gap: 30px;
    max-width: 800px;
    margin: 0 auto;
    transition: all 0.4s ease;
}

.address-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.address-icon {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #007bff, #00a6ff);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.address-icon i {
    font-size: 35px;
    color: white;
}

.address-content {
    flex-grow: 1;
}

.address-content h2 {
    color: #333;
    font-size: 2rem;
    margin-bottom: 15px;
    position: relative;
    padding-bottom: 15px;
}

.address-content h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(to right, #007bff, #00a6ff);
}

.company-name {
    font-size: 1.4rem;
    color: #007bff;
    font-weight: 600;
    margin-bottom: 20px;
}

.address-details {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.address-details p {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #555;
    font-size: 1.1rem;
    line-height: 1.6;
}

.address-details i {
    color: #007bff;
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
}

@media (max-width: 768px) {
    .address-card {
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 30px 20px;
    }

    .address-icon {
        width: 70px;
        height: 70px;
    }

    .address-icon i {
        font-size: 30px;
    }

    .address-content h2::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .address-details p {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .address-card {
        padding: 25px 15px;
    }

    .address-content h2 {
        font-size: 1.8rem;
    }

    .company-name {
        font-size: 1.2rem;
    }

    .address-details p {
        font-size: 1rem;
    }
}

/* 联系我们样式 */
.contact-section {
    margin-top: 60px;
    padding: 30px;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: center;
}

.contact-section h2 {
    color: #333;
    margin-bottom: 20px;
}

.contact-info {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-top: 20px;
}

.contact-info p {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #666;
}

.contact-info i {
    color: #007bff;
}

.contact-form {
    background: #fff;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(0,86,179,0.1);
}

.submit-btn {
    background: var(--primary-color);
    color: white;
    padding: 0.8rem 2rem;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

.submit-btn:hover {
    background: #004494;
}

.submit-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.form-message {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 4px;
    display: none;
}

.form-message.success {
    display: block;
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.form-message.error {
    display: block;
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 响应式设计补充 */
@media (max-width: 992px) {
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .baidu-map {
        height: 300px;
    }
}

@media (max-width: 768px) {
    .contact-content {
        margin-top: 2rem;
    }
    
    .contact-info {
        gap: 1.5rem;
    }
    
    .baidu-map {
        height: 250px;
    }
}

@media (max-width: 480px) {
    .contact-info {
        grid-template-columns: 1fr;
    }
}

/* 首页英雄区域样式现代化 - 优化亮度 */
.hero {
    height: 600px;
    display: flex;
    align-items: center;
    text-align: center;
    color: white;
    position: relative;
    overflow: hidden;
}

.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 40%),
        radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.08) 0%, transparent 40%),
        linear-gradient(90deg, transparent 49%, rgba(255, 255, 255, 0.05) 50%, transparent 51%),
        linear-gradient(0deg, transparent 49%, rgba(255, 255, 255, 0.03) 50%, transparent 51%);
    background-size: 120px 120px, 180px 180px, 60px 60px, 60px 60px;
    background-position: 0 0, 60px 60px, 0 0, 0 0;
    animation: gridMove 25s linear infinite;
    opacity: 0.6;
    z-index: 0;
    pointer-events: none;
}

/* 轮播图幻灯片背景 */
.swiper-slide.slide-solution {
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.6), rgba(25, 118, 210, 0.7)), url('index_pic1.jpg') no-repeat center center/cover;
}

.swiper-slide.slide-service {
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.6), rgba(25, 118, 210, 0.7)), url('index_pic2.jpg') no-repeat center center/cover;
}

.swiper-slide.slide-brands {
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.6), rgba(25, 118, 210, 0.7)), url('index_pic3.png') no-repeat center center/cover;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.05) 100%);
    z-index: 1;
}

.hero-content {
    max-width: 900px;
    margin: 0 auto;
    padding: var(--space-8);
    position: relative;
    z-index: 10;
    pointer-events: auto;
}

.hero-content h1 {
    font-size: var(--font-size-5xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--space-6);
    line-height: 1.2;
    background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 50%, #ffffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow:
        0 0 20px rgba(255,255,255,0.5),
        0 2px 8px rgba(0,0,0,0.4),
        0 4px 16px rgba(0,0,0,0.2);
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
    animation: slideUp var(--duration-slow) var(--ease-out);
    position: relative;
}

.hero-content h1::before {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.3));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    z-index: -1;
    filter: blur(1px);
}

.hero-content p {
    font-size: var(--font-size-xl);
    margin-bottom: var(--space-8);
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.95);
    text-shadow:
        0 1px 3px rgba(0,0,0,0.4),
        0 2px 6px rgba(0,0,0,0.2);
    font-weight: var(--font-weight-medium);
    letter-spacing: 0.5px;
    animation: slideUp var(--duration-slow) var(--ease-out) 0.2s;
    animation-fill-mode: forwards;
    backdrop-filter: blur(1px);
}

.hero-buttons {
    display: flex;
    gap: var(--space-6);
    justify-content: center;
    flex-wrap: wrap;
    animation: slideUp var(--duration-slow) var(--ease-out) 0.4s;
    animation-fill-mode: forwards;
    margin-top: var(--space-6);
    position: relative;
    z-index: 15;
    pointer-events: auto;
}

.hero-buttons .btn {
    min-width: 180px;
    padding: var(--space-5) var(--space-10);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    letter-spacing: 0.5px;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    transition: all var(--duration-normal) var(--ease-out);
}

.hero-buttons .btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.6s var(--ease-out);
}

.hero-buttons .btn:hover::after {
    left: 100%;
}

.hero-buttons .btn-primary {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.25) 0%,
        rgba(255, 255, 255, 0.15) 50%,
        rgba(255, 255, 255, 0.25) 100%);
    border: 2px solid rgba(255, 255, 255, 0.4);
    color: white;
    box-shadow:
        0 8px 32px rgba(0,0,0,0.3),
        inset 0 1px 0 rgba(255,255,255,0.3),
        inset 0 -1px 0 rgba(0,0,0,0.1);
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.hero-buttons .btn-primary:hover {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.35) 0%,
        rgba(255, 255, 255, 0.25) 50%,
        rgba(255, 255, 255, 0.35) 100%);
    transform: translateY(-4px) scale(1.02);
    box-shadow:
        0 12px 40px rgba(0,0,0,0.4),
        inset 0 1px 0 rgba(255,255,255,0.4),
        inset 0 -1px 0 rgba(0,0,0,0.1);
    border-color: rgba(255, 255, 255, 0.6);
}

.hero-buttons .btn-secondary {
    background: linear-gradient(135deg,
        rgba(0, 0, 0, 0.2) 0%,
        rgba(0, 0, 0, 0.1) 50%,
        rgba(0, 0, 0, 0.2) 100%);
    border: 2px solid rgba(255, 255, 255, 0.6);
    color: white;
    box-shadow:
        0 8px 32px rgba(0,0,0,0.2),
        inset 0 1px 0 rgba(255,255,255,0.2),
        inset 0 -1px 0 rgba(0,0,0,0.2);
    text-shadow: 0 1px 2px rgba(0,0,0,0.4);
}

.hero-buttons .btn-secondary:hover {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.15) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(255, 255, 255, 0.15) 100%);
    transform: translateY(-4px) scale(1.02);
    box-shadow:
        0 12px 40px rgba(0,0,0,0.3),
        inset 0 1px 0 rgba(255,255,255,0.3),
        inset 0 -1px 0 rgba(0,0,0,0.1);
    border-color: rgba(255, 255, 255, 0.8);
}

.btn {
    display: inline-block;
    padding: 1rem 2rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: var(--accent-color);
    color: white;
}

.btn-primary:hover {
    background: #3d8b40;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

/* 品牌区域样式 */
.brands {
    padding: 4rem 0;
    background-color: var(--section-bg);
}

.brands h2 {
    text-align: center;
    margin-bottom: 3rem;
    color: var(--secondary-color);
    font-size: 2.2rem;
}

.brands-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin: 0 auto;
    max-width: 1200px;
}

.brand-item {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-align: center;
}

.brand-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

.brand-item img {
    max-width: 100%;
    height: 80px;
    object-fit: contain;
    margin-bottom: 1rem;
}

.brand-item h3 {
    color: var(--dark-color);
    font-size: 1.1rem;
    margin: 0;
}

@media (max-width: 1200px) {
    .brands-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .brands-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .brand-item img {
        height: 60px;
    }
}

@media (max-width: 480px) {
    .brands-grid {
        grid-template-columns: repeat(1, 1fr);
    }
    
    .brand-item {
        padding: 1rem;
    }
}

/* 通用动态背景样式 */
.dynamic-background {
    background:
        linear-gradient(135deg, rgba(15, 23, 42, 0.12) 0%, rgba(51, 65, 85, 0.08) 100%),
        linear-gradient(180deg, #f8fafc, #f1f5f9);
    position: relative;
    overflow: hidden;
    margin: 0;
    border: none;
}

.dynamic-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(15, 23, 42, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(51, 65, 85, 0.12) 0%, transparent 50%),
        linear-gradient(90deg, transparent 49%, rgba(71, 85, 105, 0.18) 50%, transparent 51%),
        linear-gradient(0deg, transparent 49%, rgba(100, 116, 139, 0.12) 50%, transparent 51%);
    background-size: 100px 100px, 150px 150px, 50px 50px, 50px 50px;
    background-position: 0 0, 50px 50px, 0 0, 0 0;
    animation: gridMove 20s linear infinite;
    opacity: 0.85;
    z-index: -1;
    pointer-events: none;
    will-change: transform;
}

/* 确保页面布局连贯性 */
section {
    display: block;
    margin: 0;
    padding: 0;
    border: none;
    position: relative;
}

/* 热门产品样式现代化 */
.featured-products {
    padding: var(--space-20) 0;
    position: relative;
    z-index: auto;
}

/* 为热门产品区域添加额外的视觉层 */
.featured-products::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(ellipse at 20% 30%, rgba(15, 23, 42, 0.08) 0%, transparent 60%),
        radial-gradient(ellipse at 80% 70%, rgba(71, 85, 105, 0.06) 0%, transparent 60%);
    pointer-events: none;
    z-index: -1;
    animation: backgroundPulse 8s ease-in-out infinite;
}





.featured-products h2 {
    text-align: center;
    margin-bottom: var(--space-16);
    color: var(--gray-800);
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    position: relative;
}

.featured-products h2::after {
    content: '';
    position: absolute;
    bottom: -var(--space-4);
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    animation: pulse 2s ease-in-out infinite;
}

/* 动态装饰元素容器 */
.featured-products .decorative-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

/* 浮动几何图形 */
.featured-products .floating-shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(15, 23, 42, 0.25), rgba(71, 85, 105, 0.18));
    box-shadow:
        0 4px 20px rgba(15, 23, 42, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    animation: floatShape 8s ease-in-out infinite;
    backdrop-filter: blur(2px);
    border: 1px solid rgba(71, 85, 105, 0.2);
}

.featured-products .floating-shape:nth-child(1) {
    top: 20%;
    left: 10%;
    width: 60px;
    height: 60px;
    animation-delay: 0s;
}

.featured-products .floating-shape:nth-child(2) {
    top: 60%;
    right: 15%;
    width: 40px;
    height: 40px;
    animation-delay: 2s;
    border-radius: 20%;
}

.featured-products .floating-shape:nth-child(3) {
    bottom: 30%;
    left: 5%;
    width: 80px;
    height: 80px;
    animation-delay: 4s;
    border-radius: 30%;
}

.featured-products .floating-shape:nth-child(4) {
    top: 40%;
    right: 8%;
    width: 30px;
    height: 30px;
    animation-delay: 6s;
}

/* 光线效果 */
.featured-products .light-beam {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(71, 85, 105, 0.15) 40%,
        rgba(148, 163, 184, 0.35) 45%,
        rgba(255, 255, 255, 0.25) 50%,
        rgba(148, 163, 184, 0.35) 55%,
        rgba(100, 116, 139, 0.15) 60%,
        transparent 100%);
    animation: lightSweep 12s linear infinite;
    transform: skewX(-20deg);
    filter: blur(1px);
    mix-blend-mode: overlay;
}

.featured-products .container {
    position: relative;
    z-index: 2;
}

.products-grid {
    display: flex;
    justify-content: space-between;
    gap: 2rem;
    position: relative;
    z-index: 3;
    margin: 0 auto;
    max-width: 1200px;
}

.product-card {
    background: var(--gradient-card);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    transition: all var(--duration-normal) var(--ease-out);
    width: calc(50% - 1rem);
    position: relative;
    display: flex;
    flex-direction: column;
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
    opacity: 0;
    transition: opacity var(--duration-normal) var(--ease-out);
    z-index: 1;
    pointer-events: none;
}

.product-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-2xl);
    border-color: rgba(33, 150, 243, 0.3);
}

.product-card:hover::before {
    opacity: 1;
}

.product-card img {
    width: 100%;
    height: 320px;
    object-fit: contain;
    background: linear-gradient(135deg, var(--gray-50), var(--gray-100));
    padding: var(--space-6);
    position: relative;
    z-index: 2;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    transition: all var(--duration-normal) var(--ease-out);
    border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

.product-card:hover img {
    background: linear-gradient(135deg, var(--primary-50), var(--gray-100));
    transform: scale(1.05);
}

.product-info {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-info h3 {
    color: var(--secondary-color);
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.product-info p {
    color: var(--dark-color);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.product-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.btn-primary {
    background: var(--accent-color);
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid var(--accent-color);
}

.btn-primary:hover {
    background: #3d8b40;
    border-color: #3d8b40;
}

.btn-outline {
    color: var(--secondary-color);
    padding: 0.8rem 1.5rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid var(--secondary-color);
}

.btn-outline:hover {
    background: var(--secondary-color);
    color: white;
}

@media (max-width: 768px) {
    .products-grid {
        flex-direction: column;
    }
    
    .product-card {
        width: 100%;
        margin: 0 1rem 2rem;
    }
    
    .product-card img {
        height: 280px;
        padding: 1rem;
    }
    
    .product-info {
        padding: 1.5rem;
    }
    
    .product-info h3 {
        font-size: 1.3rem;
    }
    
    .product-buttons {
        flex-direction: column;
    }
    
    .btn-primary,
    .btn-outline {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .product-card {
        margin: 0 0.5rem 1.5rem;
    }
    
    .product-card img {
        height: 220px;
        padding: 0.8rem;
    }
    
    .product-info {
        padding: 1rem;
    }
}

/* 服务优势样式现代化 */
.advantages {
    padding: var(--space-20) 0;
    position: relative;
}

.advantages::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--primary-200), transparent);
}

.advantages h2 {
    text-align: center;
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-800);
    margin-bottom: var(--space-16);
    position: relative;
}

.advantages h2::after {
    content: '';
    position: absolute;
    bottom: -var(--space-4);
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
}

.advantages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-8);
}

.advantage-item {
    text-align: center;
    padding: var(--space-8);
    background: var(--gradient-card);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: var(--radius-2xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-lg);
    transition: all var(--duration-normal) var(--ease-out);
    position: relative;
    overflow: hidden;
}

.advantage-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.05), rgba(25, 118, 210, 0.1));
    opacity: 0;
    transition: opacity var(--duration-normal) var(--ease-out);
    z-index: 1;
}

.advantage-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-2xl);
    border-color: rgba(33, 150, 243, 0.3);
}

.advantage-item:hover::before {
    opacity: 1;
}

.advantage-item i {
    font-size: var(--font-size-5xl);
    color: var(--primary-600);
    margin-bottom: var(--space-6);
    transition: all var(--duration-normal) var(--ease-bounce);
    position: relative;
    z-index: 2;
}

.advantage-item:hover i {
    color: var(--primary-700);
    transform: scale(1.1) rotate(5deg);
}

.advantage-item h3 {
    color: var(--gray-800);
    margin: 0 0 var(--space-4);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    position: relative;
    z-index: 2;
}

.advantage-item p {
    color: var(--gray-600);
    margin: 0;
    line-height: 1.6;
    position: relative;
    z-index: 2;
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计补充 */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-content p {
        font-size: 1rem;
    }

    .hero-buttons {
        flex-direction: column;
    }

    .btn {
        width: 100%;
        text-align: center;
    }

    .advantages-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .brands-grid {
        grid-template-columns: 1fr;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }
}

.map-container {
    grid-column: 1 / -1;
    height: 400px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin: 2rem 0;
    position: relative;
}

.baidu-map {
    width: 100%;
    height: 100%;
}

.baidu-map iframe {
    border: none;
    width: 100%;
    height: 100%;
}

.map-overlay {
    position: absolute;
    top: 20px;
    left: 20px;
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    max-width: 300px;
}

.map-info h4 {
    color: var(--secondary-color);
    margin: 0 0 10px 0;
    font-size: 1.2rem;
}

.map-info p {
    color: var(--dark-color);
    margin: 0 0 15px 0;
    font-size: 0.95rem;
    line-height: 1.4;
}

.map-info i {
    color: var(--accent-color);
    margin-right: 8px;
}

.map-info .btn {
    display: inline-block;
    padding: 8px 16px;
    font-size: 0.9rem;
}

@media (max-width: 992px) {
    .map-container {
        height: 350px;
    }
    
    .map-overlay {
        top: 15px;
        left: 15px;
        padding: 15px;
        max-width: 250px;
    }

}

@media (max-width: 768px) {
    .map-container {
        height: 300px;
        margin: 1.5rem 0;
    }
    
    .map-overlay {
        position: relative;
        top: auto;
        left: auto;
        max-width: none;
        margin-top: -60px;
        margin-left: 15px;
        margin-right: 15px;
        background: white;
    }
}

@media (max-width: 480px) {
    .map-container {
        height: 250px;
    }
    
    .map-info h4 {
        font-size: 1.1rem;
    }
    
    .map-info p {
        font-size: 0.9rem;
    }
}

/* 微信联系样式 */
.wechat-container {
    position: relative;
    cursor: pointer;
}

.wechat-container .fab.fa-weixin {
    color: #2aae67;
    font-size: 24px;
}

.qr-code-popup {
    position: absolute;
    top: 50%;
    left: 100%;
    transform: translateY(-50%);
    background: white;
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.15);
    display: none;
    z-index: 1000;
    margin-left: 20px;
}

.qr-code-popup::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -10px;
    transform: translateY(-50%);
    border-width: 10px;
    border-style: solid;
    border-color: transparent white transparent transparent;
}

.qr-code-popup img {
    width: 150px;
    height: 150px;
    display: block;
}

.wechat-container:hover .qr-code-popup {
    display: block;
}

/* 移除旧的微信联系样式 */
.wechat-contact {
    display: none;
}

/* 产品详情样式 */
.product-description {
    margin: 20px 0;
    line-height: 1.6;
}

.product-description ul {
    list-style-type: none;
    padding-left: 0;
    margin: 15px 0;
}

.product-description li {
    margin: 8px 0;
    padding-left: 20px;
    position: relative;
}

.product-description li:before {
    content: "•";
    position: absolute;
    left: 0;
    color: #007bff;
}

.product-description .note {
    color: #dc3545;
    font-style: italic;
    margin-top: 15px;
}

.product-parameters {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.product-parameters h4 {
    margin-bottom: 15px;
    color: #333;
    font-size: 1.2em;
}

.product-parameters img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
}

/* 面包屑导航样式 */
.breadcrumb {
    background: #f8f9fa;
    padding: 15px 0;
    margin-bottom: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.breadcrumb-nav {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.95rem;
}

.breadcrumb-nav a {
    color: #007bff;
    text-decoration: none;
    transition: color 0.3s ease;
    position: relative;
}

.breadcrumb-nav a:hover {
    color: #0056b3;
}

.breadcrumb-nav a:hover::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: #007bff;
    animation: slideIn 0.3s ease;
}

.breadcrumb-nav i {
    color: #6c757d;
    font-size: 0.8rem;
}

.breadcrumb-nav span {
    color: #495057;
    font-weight: 500;
}

@keyframes slideIn {
    from {
        transform: scaleX(0);
    }
    to {
        transform: scaleX(1);
    }
}

/* 产品按钮样式 */
.product-buttons {
    display: flex;
    gap: 15px;
    margin: 30px 0;
    justify-content: center;
}

.product-buttons .btn-primary {
    background: #ff4400;
    border-color: #ff4400;
    color: white;
    padding: 12px 25px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.product-buttons .btn-primary:hover {
    background: #ff5500;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(255, 68, 0, 0.2);
}

.product-buttons .btn-outline {
    border: 2px solid #007bff;
    color: #007bff;
    padding: 12px 25px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.product-buttons .btn-outline:hover {
    background: #007bff;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 123, 255, 0.2);
}

@media (max-width: 768px) {
    .breadcrumb {
        margin-bottom: 20px;
    }

    .breadcrumb-nav {
        font-size: 0.9rem;
    }

    .product-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .product-buttons .btn-primary,
    .product-buttons .btn-outline {
        width: 100%;
        justify-content: center;
    }
}

/* 下载部分样式 */
.download-section {
    margin: 3rem 0;
    padding: 2rem;
    background-color: var(--section-bg);
    border-radius: 10px;
}

.download-section h2 {
    margin-bottom: 1.5rem;
    color: var(--secondary-color);
    font-size: 1.8rem;
}

.download-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
}

.download-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    text-decoration: none;
    color: var(--dark-color);
    transition: all 0.3s ease;
}

.download-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 10px rgba(0,0,0,0.15);
    color: var(--primary-color);
}

.download-item i {
    font-size: 1.8rem;
    color: #e74c3c;
    margin-right: 1rem;
}

.download-item span {
    font-size: 0.95rem;
    font-weight: 500;
}

@media (max-width: 768px) {
    .download-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
    
    .download-item {
        padding: 0.8rem;
    }
    
    .download-item i {
        font-size: 1.5rem;
    }
    
    .download-item span {
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .download-grid {
        grid-template-columns: 1fr;
    }
}

/* 产品应用场合样式 */
.product-applications {
    margin: 2rem 0;
}

.product-applications h2 {
    color: var(--secondary-color);
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.product-applications ul {
    list-style: none;
    padding-left: 1rem;
}

.product-applications li {
    margin-bottom: 0.8rem;
    position: relative;
    padding-left: 1.5rem;
    line-height: 1.5;
}

.product-applications li:before {
    content: '\f058';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    color: var(--accent-color);
    position: absolute;
    left: 0;
    top: 2px;
}

/* 搜索结果页面样式现代化 */
.search-results-container {
    padding: var(--space-16) 0;
    background: linear-gradient(180deg, var(--gray-50), white);
    position: relative;
    border-radius: var(--radius-2xl);
    margin: var(--space-8) 0;
}

.search-results-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e0e7ff" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    z-index: 1;
    border-radius: var(--radius-2xl);
}

/* 确保搜索内容在背景之上 */
.search-results-container > * {
    position: relative;
    z-index: 2;
}

.search-results-container h1 {
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
    font-size: 2rem;
}

.search-query {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.search-query p {
    font-size: 1.1rem;
}

.search-query span {
    font-weight: 600;
    color: var(--primary-color);
}

.search-result-item {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-item h3 {
    margin-bottom: 0.5rem;
}

.search-result-item h3 a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s;
}

.search-result-item h3 a:hover {
    color: var(--secondary-color);
}

.result-category {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.8rem;
    display: inline-block;
    background-color: #f0f0f0;
    padding: 0.2rem 0.8rem;
    border-radius: 3px;
}

.result-description {
    color: #444;
    line-height: 1.6;
}

.no-results {
    padding: 2rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    text-align: center;
}

.no-results p {
    font-size: 1.1rem;
    color: #666;
}

@media (max-width: 768px) {
    .search-results-container {
        padding: 2rem 0;
    }
    
    .search-results-container h1 {
        font-size: 1.8rem;
    }
    
    .search-query p {
        font-size: 1rem;
    }
    
    .search-result-item {
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
    }
}

@media (max-width: 480px) {
    .search-results-container h1 {
        font-size: 1.5rem;
    }
    
    .result-description {
        font-size: 0.9rem;
    }
}

/* 轮播图样式现代化 - 简化设计 */
.hero-swiper {
    width: 100%;
    height: 600px;
    position: relative;
    z-index: 5;
    overflow: hidden;
    pointer-events: auto;
}

.hero-swiper .swiper-slide {
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-swiper .swiper-pagination {
    bottom: var(--space-8);
    z-index: 1000;
    display: flex;
    justify-content: center;
    gap: var(--space-2);
    pointer-events: auto;
    position: relative;
}

.hero-swiper .swiper-pagination-bullet {
    width: 14px;
    height: 14px;
    background: rgba(255, 255, 255, 0.6);
    opacity: 1;
    border-radius: var(--radius-full);
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
    border: 2px solid rgba(255, 255, 255, 0.3);
    position: relative;
    z-index: 1001;
    pointer-events: auto;
}

.hero-swiper .swiper-pagination-bullet:hover {
    background: rgba(255, 255, 255, 0.8);
    transform: scale(1.1);
}

.hero-swiper .swiper-pagination-bullet-active {
    background: white;
    transform: scale(1.3);
    box-shadow:
        0 0 15px rgba(255, 255, 255, 0.8),
        0 0 30px rgba(33, 150, 243, 0.4);
    border-color: rgba(255, 255, 255, 0.8);
}

.swiper-slide {
    position: relative;
    background-size: cover;
    background-position: center;
    color: white;
    display: flex;
    align-items: center;
    height: 100%;
}

.swiper-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1;
}

.slide-solution {
    background: linear-gradient(135deg, #0056b3, #003366), url('index_backgroud.png') no-repeat center center/cover;
    background-blend-mode: overlay;
}

.slide-service {
    background: linear-gradient(135deg, #004d40, #00796b), url('index_backgroud.png') no-repeat center center/cover;
    background-blend-mode: overlay;
}

.slide-brands {
    background: linear-gradient(135deg, #303f9f, #1a237e), url('index_backgroud.png') no-repeat center center/cover;
    background-blend-mode: overlay;
}

.swiper-slide .container {
    position: relative;
    z-index: 2;
}

/* 删除重复的分页指示器样式 */

/* 解决方案区域样式现代化 */
.solutions-section {
    padding: var(--space-20) 0;
    position: relative;
    z-index: 1;
}

.solutions-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e0e7ff" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    z-index: -1;
}

.solutions-section .container {
    position: relative;
    z-index: 2;
}



.solutions-section h2 {
    text-align: center;
    margin-bottom: var(--space-16);
    color: var(--gray-800);
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    position: relative;
}

.solutions-section h2::after {
    content: '';
    position: absolute;
    bottom: -var(--space-4);
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--gradient-secondary);
    border-radius: var(--radius-full);
}

.solutions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-8);
}

.solution-card {
    background: var(--gradient-card);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all var(--duration-normal) var(--ease-out);
    position: relative;
    overflow: hidden;
}

.solution-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.05), rgba(25, 118, 210, 0.1));
    opacity: 0;
    transition: opacity var(--duration-normal) var(--ease-out);
    z-index: 1;
}

.solution-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-2xl);
    border-color: rgba(33, 150, 243, 0.3);
}

.solution-card:hover::before {
    opacity: 1;
}

.solution-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    color: white;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-6);
    transition: all var(--duration-normal) var(--ease-out);
    position: relative;
    z-index: 2;
    box-shadow: var(--shadow-lg);
}

.solution-icon::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: var(--gradient-secondary);
    border-radius: var(--radius-full);
    z-index: -1;
    opacity: 0;
    transition: opacity var(--duration-normal) var(--ease-out);
}

.solution-icon i {
    font-size: var(--font-size-3xl);
    transition: transform var(--duration-normal) var(--ease-bounce);
}

.solution-card:hover .solution-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--shadow-xl);
}

.solution-card:hover .solution-icon::before {
    opacity: 1;
}

.solution-card:hover .solution-icon i {
    transform: scale(1.1);
}

.solution-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--secondary-color);
}

.solution-card ul {
    list-style: none;
    padding: 0;
    margin: 0 0 1.5rem 0;
}

.solution-card ul li {
    padding: 0.5rem 0;
    position: relative;
    padding-left: 1.5rem;
    color: #555;
}

.solution-card ul li:before {
    content: '•';
    color: var(--accent-color);
    font-size: 1.2rem;
    position: absolute;
    left: 0;
    top: 0.4rem;
}

/* 产品徽章样式 */
.product-badge {
    display: none !important;
}

/* 隐藏筛选后不匹配的产品 */
.product-item.filtered {
    display: none;
}

/* 无产品提示信息样式 */
.no-products-message,
.no-category-products-message {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    padding: var(--space-8);
    text-align: center;
}

.no-products-content {
    background: var(--gradient-card);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: var(--radius-2xl);
    padding: var(--space-12);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 100%;
}

.no-products-content i {
    font-size: var(--font-size-5xl);
    color: var(--primary-400);
    margin-bottom: var(--space-6);
    opacity: 0.7;
}

.no-products-content h3 {
    color: var(--gray-800);
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-4);
}

.no-products-content p {
    color: var(--gray-600);
    font-size: var(--font-size-lg);
    margin-bottom: var(--space-8);
    line-height: 1.6;
}

.message-actions {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    flex-wrap: wrap;
}

.message-actions .btn {
    min-width: 160px;
    padding: var(--space-3) var(--space-6);
}

/* 无产品提示移动端优化 */
@media (max-width: 768px) {
    .no-products-content {
        padding: var(--space-8);
        margin: var(--space-4);
    }

    .no-products-content i {
        font-size: var(--font-size-4xl);
    }

    .no-products-content h3 {
        font-size: var(--font-size-xl);
    }

    .no-products-content p {
        font-size: var(--font-size-base);
    }

    .message-actions {
        flex-direction: column;
    }

    .message-actions .btn {
        width: 100%;
        min-width: auto;
    }
}

/* 产品元数据样式 */
.product-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.product-brand, .product-category {
    font-size: 0.9rem;
    color: #666;
    background: #f0f0f0;
    padding: 0.2rem 0.6rem;
    border-radius: 3px;
}

/* 品牌合作部分样式现代化 */
.brands-section {
    padding: var(--space-20) 0;
    position: relative;
    z-index: 1;
}

.brands-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--primary-200), transparent);
    z-index: -1;
}

.brands-section .container {
    position: relative;
    z-index: 2;
}

.brands-section h2 {
    text-align: center;
    margin-bottom: var(--space-16);
    position: relative;
    color: var(--gray-800);
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
}

.brands-section h2::after {
    content: '';
    position: absolute;
    bottom: -var(--space-4);
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .hero-swiper {
        height: 400px;
    }
    
    .solutions-grid {
        grid-template-columns: 1fr;
    }
    
    .solution-card {
        padding: 1.5rem;
    }
    
    .solution-icon {
        width: 60px;
        height: 60px;
    }
    
    .solution-icon i {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .hero-swiper {
        height: 350px;
    }
    
    .hero-content h1 {
        font-size: 1.8rem;
    }
    
    .hero-content p {
        font-size: 1rem;
    }
    
    .solution-card {
        padding: 1.2rem;
    }
}

/* 产品中心页面布局 */
.products-layout {
    display: flex;
    gap: 30px;
    margin-top: 30px;
}

/* 左侧导航栏样式现代化 */
.products-sidebar {
    width: 320px;
    flex-shrink: 0;
    background: var(--gradient-card);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: var(--radius-2xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.sidebar-title {
    background: var(--gradient-primary);
    color: white;
    padding: var(--space-5) var(--space-6);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
}

.sidebar-title::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
    opacity: 0;
    transition: opacity var(--duration-normal) var(--ease-out);
}

.sidebar-title:hover::before {
    opacity: 1;
}

.sidebar-category {
    border-bottom: 1px solid #eee;
}

.category-header {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    cursor: pointer;
    transition: all 0.3s;
    font-weight: normal;
    --major-color: #1e50ae;
    text-align: left;
    border-spacing: 0;
    font-family: 微软雅黑;
    color: rgb(102, 102, 102);
    font-size: 14px;
    position: relative;
}

.category-header:hover {
    background-color: #f5f5f5;
    color: var(--primary-color);
}

.category-header i:first-child {
    margin-right: 10px;
    font-size: 16px;
    color: var(--primary-color);
    width: 20px;
    text-align: center;
}

.category-header span {
    flex: 1;
    font-weight: 500;
}

.category-header .fa-chevron-down {
    font-size: 14px;
    transition: transform 0.3s;
    transform: rotate(180deg); /* 默认箭头向上 */
    color: #999;
    padding: 5px;
    border-radius: 50%;
    background-color: #f0f0f0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.category-header:hover .fa-chevron-down {
    background-color: #e0e0e0;
    color: var(--primary-color);
}

.sidebar-category.collapsed .fa-chevron-down {
    transform: rotate(0deg); /* 折叠状态下箭头向下 */
}

/* 移除悬停时的自动展开效果 */
.sidebar-category:hover .category-submenu {
    max-height: 500px; /* 保持与默认相同 */
}

.sidebar-category:hover .fa-chevron-down {
    transform: rotate(180deg); /* 保持与默认相同 */
}

.category-submenu {
    max-height: 500px; /* 默认展开，设置足够大的高度 */
    overflow: hidden;
    transition: max-height 0.3s ease;
    padding: 0;
    margin: 0;
    list-style: none;
    background-color: #f9f9f9;
}

.sidebar-category.collapsed .category-submenu {
    max-height: 0; /* 折叠状态下隐藏子菜单 */
}

.category-submenu li {
    padding: 0;
    margin: 0;
}

.category-submenu li a {
    display: block;
    padding: 10px 20px 10px 50px;
    color: #666;
    font-size: 14px;
    text-decoration: none;
    transition: all 0.2s;
    position: relative;
}

.category-submenu li a:before {
    content: '';
    position: absolute;
    left: 30px;
    top: 50%;
    width: 5px;
    height: 5px;
    background-color: #ccc;
    border-radius: 50%;
    transform: translateY(-50%);
    transition: all 0.2s;
}

.category-submenu li a:hover {
    color: var(--primary-color);
    background-color: #f0f0f0;
}

.category-submenu li a:hover:before {
    background-color: var(--primary-color);
}

/* 菜单项激活状态 */
.category-submenu li a.active {
    color: var(--primary-color);
    background-color: #f0f0f0;
    font-weight: 500;
}

.category-submenu li a.active:before {
    background-color: var(--primary-color);
    width: 7px;
    height: 7px;
}

/* 右侧产品展示样式 */
.products-content {
    flex: 1;
}

.product-section {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid #eee;
}

.product-section h2 {
    font-size: 22px;
    color: var(--primary-color);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    position: relative;
}

.product-section h2:after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 80px;
    height: 3px;
    background: var(--primary-color);
}

.product-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 25px;
}

/* 统一产品展示容器样式 */
.unified-products-container {
    width: 100%;
    padding: 20px 0;
}

.unified-products-container h2 {
    color: var(--gray-800);
    font-size: var(--font-size-2xl);
    font-weight: 600;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--primary-500);
    position: relative;
}

.unified-products-container h2::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: var(--primary-600);
}

.unified-products-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
    width: 100%;
}

/* 响应式调整 - 统一产品网格 */
@media (max-width: 1200px) {
    .unified-products-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 992px) {
    .unified-products-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .unified-products-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .unified-products-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}

.product-item {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
    display: flex;
    flex-direction: column;
}

.product-item:hover {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.product-image {
    height: 200px;
    overflow: hidden;
    background-color: #f9f9f9;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: all 0.3s;
}

.product-item:hover .product-image img {
    transform: scale(1.05);
}

.product-details {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-details h3 {
    font-size: 18px;
    margin: 0 0 10px;
    color: var(--dark-color);
}

.product-details p {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    margin: 0 0 15px;
    flex: 1;
}

.btn-more {
    display: inline-block;
    padding: 8px 15px;
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s;
    align-self: flex-start;
    margin-top: auto;
}

.btn-more:hover {
    background-color: var(--secondary-color);
}

/* 响应式调整 */
@media (max-width: 992px) {
    .products-layout {
        flex-direction: column;
    }
    
    .products-sidebar {
        width: 100%;
        margin-bottom: 30px;
    }
    
    .product-list {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .product-list {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
    
    .product-details h3 {
        font-size: 16px;
    }
    
    .product-details p {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .product-list {
        grid-template-columns: 1fr;
    }
    
    .product-image {
        height: 180px;
    }
}

/* 响应式侧边栏切换按钮 */
.sidebar-toggle {
    display: none;
    width: 100%;
    padding: 12px 15px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    margin-bottom: 15px;
    font-size: 16px;
    cursor: pointer;
    text-align: left;
}

.sidebar-toggle i {
    margin-right: 10px;
}

.sidebar-toggle.active {
    background: var(--secondary-color);
}

@media (max-width: 992px) {
    .sidebar-toggle {
        display: flex;
        align-items: center;
    }
    
    .products-sidebar {
        display: none;
        margin-bottom: 30px;
    }
    
    .products-sidebar.active {
        display: block;
    }
    
    .products-layout {
        flex-direction: column;
    }
    
    .products-sidebar {
        width: 100%;
    }
    
    .product-list {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .product-list {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
    
    .product-details h3 {
        font-size: 16px;
    }
    
    .product-details p {
        font-size: 13px;
    }
}

/* 产品欢迎提示 */
.product-welcome {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 30px;
    text-align: center;
}

.welcome-message {
    max-width: 500px;
}

.welcome-message i {
    font-size: 40px;
    color: var(--primary-color);
    margin-bottom: 20px;
    animation: pointLeft 1.5s infinite;
}

.welcome-message h2 {
    font-size: 24px;
    color: var(--dark-color);
    margin-bottom: 15px;
}

.welcome-message p {
    font-size: 16px;
    color: #666;
}

@keyframes pointLeft {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(-15px); }
}

/* 空产品类别 */
.empty-category {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    background-color: #f9f9f9;
    border-radius: 8px;
    width: 100%;
    text-align: center;
}

.empty-category i {
    font-size: 30px;
    color: #999;
    margin-bottom: 15px;
}

.empty-category p {
    font-size: 16px;
    color: #666;
}

/* 产品详情页样式 */
.product-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 30px;
}

.product-header h1 {
    font-size: 28px;
    color: var(--primary-color);
    margin-bottom: 25px;
    text-align: center;
}

.product-image {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30px;
    background-color: #f9f9f9;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.product-image img {
    max-width: 100%;
    max-height: 400px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.product-image:hover img {
    transform: scale(1.02);
}

.parameter-image {
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    margin-top: 20px;
    text-align: center;
}

.parameter-image img {
    max-width: 100%;
    max-height: 500px;
    object-fit: contain;
}

@media (max-width: 768px) {
    .product-header h1 {
        font-size: 24px;
    }
    
    .product-image {
        padding: 20px;
    }
    
    .product-image img {
        max-height: 300px;
    }
    
    .parameter-image img {
        max-height: 400px;
    }
}

@media (max-width: 480px) {
    .product-header h1 {
        font-size: 22px;
    }
    
    .product-image {
        padding: 15px;
    }
    
    .product-image img {
        max-height: 250px;
    }
    
    .parameter-image img {
        max-height: 300px;
    }
}

/* 产品详情页整体布局 */
.product-detail {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px 0 40px;
}

/* 产品头部区域 */
.product-detail .product-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 40px;
    width: 100%;
}

.product-detail .product-header h1 {
    font-size: 32px;
    color: var(--primary-color);
    margin-bottom: 30px;
    text-align: center;
    font-weight: 600;
}

/* 产品中心页面的产品图片样式 */
.product-item .product-image {
    height: 200px;
    overflow: hidden;
    background-color: #f9f9f9;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border-radius: 8px 8px 0 0;
    min-height: auto;
    padding: 0;
    margin-bottom: 0;
}

.product-item .product-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: all 0.3s;
    max-height: none;
    max-width: 100%;
}

/* 产品详情页的产品图片样式 */
.product-detail .product-image {
    width: 100%;
    min-height: 500px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 40px;
    background-color: #f9f9f9;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.product-detail .product-image img {
    width: auto;
    height: auto;
    max-width: 90%;
    max-height: 500px;
    object-fit: contain;
    transition: transform 0.4s ease;
}

.product-detail .product-image:hover img {
    transform: scale(1.03);
}

/* 产品信息区域 */
.product-detail .product-info {
    margin-bottom: 40px;
}

.product-detail .product-info h2 {
    font-size: 24px;
    color: var(--primary-color);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.product-detail .product-features,
.product-detail .product-description,
.product-detail .product-applications {
    margin-bottom: 30px;
}

/* 技术参数图片样式 */
.product-detail .technical-parameters {
    margin-top: 40px;
}

.product-detail .technical-parameters h2 {
    font-size: 24px;
    color: var(--primary-color);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #eee;
}

.product-detail .parameter-image {
    width: 100%;
    min-height: 400px;
    background-color: #f9f9f9;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 3px 12px rgba(0,0,0,0.08);
    margin-top: 20px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
}

.product-detail .parameter-image img {
    width: auto;
    height: auto;
    max-width: 95%;
    max-height: 600px;
    object-fit: contain;
}

/* 响应式调整 - 产品中心页 */
@media (max-width: 768px) {
    .product-item .product-image {
        height: 180px;
    }
}

@media (max-width: 480px) {
    .product-item .product-image {
        height: 160px;
    }
}

/* 响应式调整 - 产品详情页 */
@media (max-width: 992px) {
    .product-detail .product-image {
        min-height: 400px;
        padding: 30px;
    }
    
    .product-detail .product-image img {
        max-height: 450px;
    }
    
    .product-detail .parameter-image {
        min-height: 350px;
    }
    
    .product-detail .parameter-image img {
        max-height: 500px;
    }
}

@media (max-width: 768px) {
    .product-detail .product-header h1 {
        font-size: 28px;
    }
    
    .product-detail .product-info h2 {
        font-size: 22px;
    }
    
    .product-detail .product-image {
        min-height: 350px;
        padding: 25px;
    }
    
    .product-detail .product-image img {
        max-height: 400px;
    }
    
    .product-detail .parameter-image {
        min-height: 300px;
        padding: 20px;
    }
    
    .product-detail .parameter-image img {
        max-height: 400px;
    }
}

@media (max-width: 480px) {
    .product-detail .product-header h1 {
        font-size: 24px;
    }
    
    .product-detail .product-info h2 {
        font-size: 20px;
    }
    
    .product-detail .product-image {
        min-height: 300px;
        padding: 20px;
    }
    
    .product-detail .product-image img {
        max-height: 350px;
    }
    
    .product-detail .parameter-image {
        min-height: 250px;
        padding: 15px;
    }
    
    .product-detail .parameter-image img {
        max-height: 350px;
    }
}

/* 品牌筛选样式现代化 */
.brand-filter {
    margin-bottom: var(--space-6);
    padding: 0 var(--space-5);
    max-height: 350px;
    overflow-y: auto;
    transition: all var(--duration-normal) var(--ease-out);
}

.brand-filter::-webkit-scrollbar {
    width: 6px;
}

.brand-filter::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius-full);
}

.brand-filter::-webkit-scrollbar-thumb {
    background: var(--primary-400);
    border-radius: var(--radius-full);
    transition: background var(--duration-normal) var(--ease-out);
}

.brand-filter::-webkit-scrollbar-thumb:hover {
    background: var(--primary-600);
}

.sidebar-title {
    position: relative;
    cursor: pointer;
}

.sidebar-title .fa-chevron-down {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.3s ease;
    font-size: 14px;
    color: var(--secondary-color);
}

.sidebar-title.collapsed .fa-chevron-down {
    transform: translateY(-50%) rotate(-90deg);
}

.sidebar-title.collapsed + .brand-filter {
    max-height: 0;
    overflow: hidden;
    opacity: 0;
    margin: 0;
    padding-top: 0;
    padding-bottom: 0;
}

.brand-item {
    margin: var(--space-2) 0;
    display: flex;
    align-items: center;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-lg);
    transition: all var(--duration-normal) var(--ease-out);
    position: relative;
}

.brand-item:hover {
    background: rgba(33, 150, 243, 0.05);
    transform: translateX(4px);
}

.brand-checkbox {
    margin-right: var(--space-3);
    cursor: pointer;
    width: 18px;
    height: 18px;
    accent-color: var(--primary-600);
    transition: all var(--duration-normal) var(--ease-out);
}

.brand-checkbox:hover {
    transform: scale(1.1);
}

.brand-item label {
    cursor: pointer;
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    transition: all var(--duration-normal) var(--ease-out);
    flex: 1;
    user-select: none;
}

.brand-item:hover label {
    color: var(--primary-700);
    font-weight: var(--font-weight-medium);
}

/* 删除重复的筛选样式 */

.small-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 18px 18px;
    align-items: stretch;
}
.small-card {
    max-width: 260px;
    min-width: 0;
    padding: 14px 10px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    font-size: 14px;
    margin: 0 auto;
}
.small-card img {
    max-width: 90%;
    height: 110px;
    object-fit: contain;
    margin-bottom: 8px;
}
.small-card .product-info h3 {
    font-size: 16px;
    margin: 8px 0 6px 0;
}
.small-card .product-info p {
    font-size: 13px;
    color: #555;
    margin-bottom: 8px;
}
.small-card .product-meta {
    font-size: 12px;
    color: #888;
    margin-bottom: 2px;
}
.small-card .product-buttons {
    display: flex;
    gap: 8px;
}
@media (max-width: 700px) {
    .small-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    .small-card {
        max-width: 100%;
        padding: 10px 4px;
    }
    .small-card img {
        height: 80px;
    }
}

.better-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 28px 24px;
    align-items: stretch;
}
.better-card {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    max-width: 320px;
    min-width: 0;
    min-height: 370px;
    height: 100%;
    padding: 20px 16px 16px 16px;
    transition: box-shadow 0.2s;
    margin: 0 auto;
    position: relative;
}
.better-card:hover {
    box-shadow: 0 6px 24px rgba(0,0,0,0.13);
}
.better-card img {
    max-width: 90%;
    max-height: 140px;
    object-fit: contain;
    margin-bottom: 12px;
    display: block;
}
.better-card .product-info {
    flex: 1 1 auto;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
}
.better-card .product-info h3 {
    font-size: 17px;
    margin: 8px 0 6px 0;
    text-align: center;
    font-weight: 600;
}
.better-card .product-info p {
    font-size: 13px;
    color: #555;
    margin-bottom: 10px;
    text-align: center;
    min-height: 38px;
}
.better-card .product-meta {
    font-size: 12px;
    color: #888;
    margin-bottom: 2px;
    text-align: center;
}
.better-card .product-buttons {
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: auto;
}
@media (max-width: 900px) {
    .better-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    .better-card {
        max-width: 100%;
        min-height: 320px;
        padding: 12px 4px;
    }
    .better-card img {
        max-height: 90px;
    }
}

.pro-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 32px 28px;
    align-items: stretch;
    justify-items: center;
}
.pro-card {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    background: #fff;
    border-radius: 18px;
    box-shadow: 0 2px 16px rgba(0,0,0,0.10);
    width: 100%;
    max-width: 340px;
    min-width: 0;
    height: 390px;
    padding: 0;
    position: relative;
    overflow: hidden;
    transition: box-shadow 0.2s;
}
.pro-card:hover {
    box-shadow: 0 8px 32px rgba(0,0,0,0.16);
}
.pro-img-area {
    width: 100%;
    height: 160px;
    background: #f7f8fa;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top-left-radius: 18px;
    border-top-right-radius: 18px;
}
.pro-img-area img {
    max-width: 90%;
    max-height: 130px;
    object-fit: contain;
    display: block;
}
.pro-divider {
    width: 90%;
    height: 1px;
    background: #e5e6eb;
    margin: 0 auto;
}
.pro-content {
    flex: 1 1 auto;
    width: 100%;
    padding: 10px 18px 0 18px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    text-align: center;
}
.pro-content h3 {
    font-size: 17px;
    margin: 8px 0 6px 0;
    font-weight: 600;
}
.pro-content p {
    font-size: 13px;
    color: #555;
    margin-bottom: 8px;
    min-height: 36px;
}
.pro-content .product-meta {
    font-size: 12px;
    color: #888;
    margin-bottom: 2px;
}
.pro-btn-area {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    padding: 0 0 28px 0;
    margin-top: auto;
}
@media (max-width: 900px) {
    .pro-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    .pro-card {
        max-width: 100%;
        height: 320px;
    }
    .pro-img-area {
        height: 110px;
    }
}

/* ==================== 增强交互效果样式 ==================== */

/* 页面加载动画样式 */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-900), var(--primary-700));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    transition: opacity 0.5s ease;
}

.page-loader.fade-out {
    opacity: 0;
}

.loader-content {
    text-align: center;
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
    padding: 0;
    box-sizing: border-box;
}

.loader-logo img {
    height: 80px;
    margin-bottom: 2rem;
    animation: float 2s ease-in-out infinite;
}

.loader-progress {
    width: 300px;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin: 2rem auto;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-300), white);
    border-radius: 2px;
    transition: width 0.3s ease;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.loader-text {
    font-size: 1.2rem;
    opacity: 0.9;
    animation: pulse 1.5s ease-in-out infinite;
    text-align: center;
    width: 300px;
    display: block;
    margin: 0 auto;
    white-space: nowrap;
    overflow: visible;
    line-height: 1.4;
    letter-spacing: 0.5px;
    padding: 0;
    box-sizing: border-box;
}

/* 粒子背景画布 */
.particle-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

/* 自定义鼠标光标 */
.custom-cursor {
    position: fixed;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    transition: all 0.2s ease;
    backdrop-filter: blur(2px);
    background: rgba(255, 255, 255, 0.1);
}

.custom-cursor.cursor-hover {
    width: 40px;
    height: 40px;
    border-color: var(--primary-400);
    background: rgba(33, 150, 243, 0.2);
    transform: scale(1.2);
}

/* 卡片光影效果 */
.card-light {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    pointer-events: none;
    animation: cardShine 1.5s ease-in-out;
}

@keyframes cardShine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* 热门产品区域动画 */
@keyframes gridMove {
    0% { transform: translate(0, 0); }
    25% { transform: translate(-10px, -10px); }
    50% { transform: translate(10px, -5px); }
    75% { transform: translate(-5px, 10px); }
    100% { transform: translate(0, 0); }
}

@keyframes floatShape {
    0%, 100% {
        transform: translateY(0) rotate(0deg) scale(1);
        opacity: 0.8;
        box-shadow: 0 4px 20px rgba(15, 23, 42, 0.15);
    }
    25% {
        transform: translateY(-25px) rotate(90deg) scale(1.1);
        opacity: 0.9;
        box-shadow: 0 8px 30px rgba(15, 23, 42, 0.2);
    }
    50% {
        transform: translateY(-15px) rotate(180deg) scale(0.9);
        opacity: 0.6;
        box-shadow: 0 6px 25px rgba(71, 85, 105, 0.18);
    }
    75% {
        transform: translateY(-35px) rotate(270deg) scale(1.05);
        opacity: 0.85;
        box-shadow: 0 10px 35px rgba(15, 23, 42, 0.25);
    }
}

@keyframes lightSweep {
    0% {
        left: -100%;
        opacity: 0;
        transform: skewX(-20deg) scaleY(0.8);
    }
    10% {
        opacity: 0.6;
        transform: skewX(-20deg) scaleY(1);
    }
    50% {
        opacity: 1;
        transform: skewX(-20deg) scaleY(1.1);
    }
    90% {
        opacity: 0.6;
        transform: skewX(-20deg) scaleY(1);
    }
    100% {
        left: 100%;
        opacity: 0;
        transform: skewX(-20deg) scaleY(0.8);
    }
}

@keyframes backgroundPulse {
    0%, 100% {
        opacity: 0.6;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: translateX(-50%) scaleX(1);
    }
    50% {
        opacity: 0.7;
        transform: translateX(-50%) scaleX(1.2);
    }
}



/* 滚动进度指示器 */
.scroll-indicator {
    position: fixed;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.indicator-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
}

.indicator-dot.active {
    background: var(--primary-500);
    border-color: var(--primary-300);
    transform: scale(1.3);
    box-shadow: 0 0 15px rgba(33, 150, 243, 0.6);
}

/* 增强的滑动动画 */
.slide-animate {
    animation: slideEnhanced 1s ease-out;
}

@keyframes slideEnhanced {
    0% {
        opacity: 0;
        transform: translateY(50px) scale(0.9);
    }
    50% {
        opacity: 0.7;
        transform: translateY(-10px) scale(1.02);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 打字机效果 */
.typewriter {
    overflow: hidden;
    border-right: 2px solid white;
    white-space: nowrap;
    animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: white; }
}

/* 增强的产品卡片3D效果 */
.product-card, .pro-card {
    perspective: 1000px;
    transform-style: preserve-3d;
}

.product-card:hover, .pro-card:hover {
    animation: cardFloat 0.6s ease-out;
}

@keyframes cardFloat {
    0% { transform: translateY(0) rotateX(0) rotateY(0); }
    50% { transform: translateY(-20px) rotateX(10deg) rotateY(10deg); }
    100% { transform: translateY(-15px) rotateX(5deg) rotateY(5deg); }
}

/* 磁性按钮效果增强 */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
    transition: all 0.3s ease;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
}

.btn:hover::after {
    width: 300px;
    height: 300px;
}

/* 视差滚动增强 */
.parallax-element {
    will-change: transform;
    transition: transform 0.1s ease-out;
}

/* 页面加载完成后的body样式 */
body.loaded {
    overflow: visible;
}

body.loaded .hero-content h1 {
    animation: slideUp 1s ease-out 0.5s both;
}

body.loaded .hero-content p {
    animation: slideUp 1s ease-out 0.7s both;
}

body.loaded .hero-buttons {
    animation: slideUp 1s ease-out 0.9s both;
}

/* 增强的悬停效果 */
.enhanced-hover {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.enhanced-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* 光线追踪效果 */
.light-trail {
    position: relative;
    overflow: hidden;
}

.light-trail::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.light-trail:hover::before {
    left: 100%;
}

/* 移动端性能优化和布局修复 */
@media (max-width: 768px) {
    /* 全局布局溢出修复 */
    html, body {
        overflow-x: hidden !important;
        max-width: 100vw !important;
        position: relative;
    }

    *, *::before, *::after {
        /* 性能优化 */
        animation-duration: 0.01ms !important;
        animation-delay: -1ms !important;
        animation-iteration-count: 1 !important;
        background-attachment: initial !important;
        scroll-behavior: auto !important;

        /* 布局溢出修复 */
        max-width: 100% !important;
        box-sizing: border-box !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
    }

    /* 容器宽度限制 */
    .container {
        max-width: 100vw !important;
        padding-left: 1rem !important;
        padding-right: 1rem !important;
        margin-left: auto !important;
        margin-right: auto !important;
    }

    /* 图片和媒体元素响应式 */
    img, video, iframe, canvas, svg {
        max-width: 100% !important;
        height: auto !important;
        display: block;
    }

    /* 表格响应式 */
    table {
        width: 100% !important;
        table-layout: fixed !important;
        word-wrap: break-word !important;
    }

    /* 文本内容防溢出 */
    h1, h2, h3, h4, h5, h6, p, span, div {
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
        hyphens: auto !important;
    }

    /* 禁用视差效果 */
    .hero::after,
    .dynamic-background::before,
    .featured-products::after {
        animation: none !important;
        transform: none !important;
    }

    /* 禁用浮动装饰元素 */
    .floating-shape,
    .light-beam {
        display: none !important;
    }

    /* 简化hover效果和交互动画 */
    .product-card:hover,
    .service-card:hover,
    .partner-item:hover,
    .advantage-item:hover,
    .solution-card:hover {
        transform: none !important;
        animation: none !important;
        transition: none !important;
    }

    /* 禁用复杂的CSS效果以提升性能 */
    .hero-swiper .swiper-slide,
    .products-grid,
    .advantages-grid {
        will-change: auto !important;
        transform: none !important;
    }

    /* 简化阴影效果 */
    .product-card,
    .service-card,
    .partner-item {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
    }

    /* 禁用backdrop-filter以提升性能 */
    .dropdown-menu,
    .partner-item {
        backdrop-filter: none !important;
        -webkit-backdrop-filter: none !important;
    }

    /* 导航菜单布局修复 */
    .header-nav ul {
        flex-wrap: wrap !important;
        justify-content: center !important;
        max-width: 100% !important;
    }

    .nav-item {
        flex-shrink: 1 !important;
        min-width: 0 !important;
    }

    .dropdown-menu {
        left: 0 !important;
        right: 0 !important;
        max-width: 100vw !important;
        transform: none !important;
    }

    /* 产品网格布局修复 */
    .products-grid,
    .advantages-grid,
    .solutions-grid {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    /* 卡片组件布局修复 */
    .product-card,
    .service-card,
    .advantage-item,
    .solution-card {
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 !important;
        padding: 1rem !important;
    }

    /* 按钮组布局修复 */
    .hero-buttons,
    .product-buttons {
        flex-direction: column !important;
        width: 100% !important;
        gap: 0.5rem !important;
    }

    .btn {
        width: 100% !important;
        max-width: 100% !important;
        text-align: center !important;
        padding: 0.75rem 1rem !important;
    }

    /* 表单元素布局修复 */
    .search-box,
    input, textarea, select {
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
    }

    .custom-cursor {
        display: none;
    }

    .scroll-indicator {
        display: none;
    }

    .loader-progress {
        width: 250px;
    }



    /* 热门产品区域移动端优化 */
    .dynamic-background {
        background:
            linear-gradient(135deg, rgba(15, 23, 42, 0.08) 0%, rgba(51, 65, 85, 0.05) 100%),
            linear-gradient(180deg, #f8fafc, #f1f5f9);
    }

    .dynamic-background::before {
        background-image:
            radial-gradient(circle at 25% 25%, rgba(15, 23, 42, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(51, 65, 85, 0.08) 0%, transparent 50%),
            linear-gradient(90deg, transparent 49%, rgba(71, 85, 105, 0.12) 50%, transparent 51%),
            linear-gradient(0deg, transparent 49%, rgba(100, 116, 139, 0.08) 50%, transparent 51%);
        background-size: 30px 30px, 50px 50px, 20px 20px, 20px 20px;
        animation-duration: 30s;
        opacity: 0.7;
    }

    .featured-products .floating-shape {
        display: none;
    }

    .featured-products .light-beam {
        animation-duration: 18s;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(71, 85, 105, 0.1) 40%,
            rgba(148, 163, 184, 0.25) 45%,
            rgba(255, 255, 255, 0.15) 50%,
            rgba(148, 163, 184, 0.25) 55%,
            rgba(100, 116, 139, 0.1) 60%,
            transparent 100%);
    }

    .featured-products::after {
        opacity: 0.4;
        animation-duration: 12s;
    }

    .featured-products::after {
        width: 100px;
        height: 100px;
        filter: blur(20px);
    }
}

@media (max-width: 480px) {
    .particle-canvas {
        display: none;
    }
}

/* 性能优化 */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
}

/* 产品页面增强交互效果 */
.product-item.enhanced-hover {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    perspective: 1000px;
    transform-style: preserve-3d;
}

.product-item.enhanced-hover:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.product-item.light-trail {
    position: relative;
    overflow: hidden;
}

.product-item.light-trail::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
    z-index: 1;
}

.product-item.light-trail:hover::before {
    left: 100%;
}

/* 产品卡片光影效果 */
.product-item .card-light {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    pointer-events: none;
    animation: cardShine 1.5s ease-in-out;
    z-index: 2;
}

/* 滚动动画 */
.animate-slide-up {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 磁性按钮增强 */
.magnetic-button {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.magnetic-button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
    transition: all 0.3s ease;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
}

.magnetic-button:hover::after {
    width: 200px;
    height: 200px;
}

/* 产品图片增强效果 */
.product-item .product-image {
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-lg);
}

.product-item .product-image img {
    transition: transform 0.4s ease;
}

.product-item:hover .product-image img {
    transform: scale(1.05);
}

/* 产品详情增强 */
.product-item .product-details {
    position: relative;
    z-index: 3;
}

/* 减少动画对性能的影响 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 小米浏览器特殊优化 - 防止缩放循环 */
.xiaomi-browser {
    /* 强制禁用所有可能导致缩放的效果 */
    overflow-x: hidden !important;
    position: relative !important;
    width: 100% !important;
    max-width: 100vw !important;
}

.xiaomi-browser * {
    /* 完全禁用变换和动画 */
    transform: none !important;
    animation: none !important;
    transition: none !important;
    will-change: auto !important;
    perspective: none !important;
    transform-style: flat !important;

    /* 强制布局稳定 */
    max-width: 100% !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
}

.xiaomi-browser .dynamic-background,
.xiaomi-browser .hero,
.xiaomi-browser .featured-products {
    background: #f8fafc !important;
    position: static !important;
    overflow: hidden !important;
}

.xiaomi-browser .dynamic-background::before,
.xiaomi-browser .dynamic-background::after,
.xiaomi-browser .hero::before,
.xiaomi-browser .hero::after,
.xiaomi-browser .featured-products::before,
.xiaomi-browser .featured-products::after {
    display: none !important;
}

.xiaomi-browser .floating-shape,
.xiaomi-browser .light-beam,
.xiaomi-browser .particle-canvas {
    display: none !important;
}

/* 针对访问电脑版模式的额外优化 */
@media screen and (min-width: 769px) {
    .mobile-device.xiaomi-browser {
        /* 电脑版模式下的特殊处理 */
        position: relative !important;
        overflow-x: hidden !important;
        max-width: 100vw !important;
    }

    .mobile-device.xiaomi-browser * {
        max-width: 100% !important;
        transform: none !important;
        animation: none !important;
        transition: none !important;
    }

    .mobile-device.xiaomi-browser .container {
        max-width: 1200px !important;
        margin: 0 auto !important;
        padding: 0 2rem !important;
    }

    .mobile-device.xiaomi-browser .hero-swiper {
        height: 400px !important;
        overflow: hidden !important;
    }

    .mobile-device.xiaomi-browser .products-grid {
        display: grid !important;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
        gap: 2rem !important;
    }
}
